import codecs
import logging
import structlog
from pathlib import Path

import pytest
from pypdf import PdfReader

logging.basicConfig(level=logging.INFO)

logger = structlog.get_logger()


def show_metadata(p: Path):
    logger.info(f"show metadata for p={p}")
    assert p.exists()
    codecs.open(str(p), "rb", encoding="latin-1")

    reader = PdfReader(str(p))

    pdf_info = reader.metadata
    logger.info(f"doc_info={pdf_info}")

    logger.info(f"title = {pdf_info.title}")
    logger.info(f"subject = {pdf_info.subject}")

    # pdf_meta = reader.
    # self.year = str(pdf_meta.xmp_createDate)
    # meta = reader.metadata

    # print(pdf_meta)


@pytest.mark.django_db
def test_show_metadata():
    root = Path("/app/dossier_test_exchange/sales_pitch_mix_with_errors_dossier")
    if root.exists():
        for p in root.glob("210*.*"):
            show_metadata(p)
            break
