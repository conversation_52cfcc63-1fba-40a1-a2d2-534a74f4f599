import uuid
from datetime import datetime, timezone
from functools import cmp_to_key
from io import BytesIO
from typing import List, <PERSON><PERSON>, Dict, Optional
from uuid import UUID

import pypdf
import django
import requests
import structlog
from django.contrib.postgres.aggregates import ArrayAgg
from django.db.models import (
    Bo<PERSON>anField,
    Count,
    ExpressionWrapper,
    F,
    Q,
    QuerySet,
)

from bekb.bekb_instance_type import get_bekb_instance_type, has_businesscases
from bekb.schemas import schemas as schemasBekb
from bekb.collaterals import (
    prepare_collateral,
    collateral_assignment_status,
    get_collateral_requirements_satisfaction_options_for_account,
    get_collaterals_by_businesscase,
    CollateralWithAssignments,
)
from bekb.models import (
    BEKBDossierProperties,
    BusinessCase,
    Attribute,
    Partner,
)
from dossier import helpers as helpers_v1
from dossier import models, schemas
from dossier.doc_cat_helpers import get_document_categories_by_uuid
from dossier.helpers import (
    prepare_original_files,
    prepare_dossier_files,
    prepare_extracted_files_data,
    prepare_processed_files_for_api,
    prepare_for_api_dossier_data,
)
from dossier.helpers_filename import create_filenames_unique
from dossier.models import (
    Dossier,
    Account,
    annotate_with_calculated_access_mode_dossier,
)
from dossier.page_objects import (
    get_semantic_pages_object,
    check_page_object_title,
    get_serialized_pages_objects,
)
from dossier.schemas import DateRange, SemanticDossier
from dossier_zipper.helpers import rotate_pdf
from processed_file.models import ProcessedFile, ProcessedPage
from projectconfig.settings import ENABLE_ERROR_DETAIL_IN_BACKEND
from semantic_document import models as semantic_document_models
from semantic_document.schemas_page_annotations import UserAnnotationsSchema
from semantic_document.models import (
    SemanticDocument,
    SemanticPageUserAnnotations,
)

logger = structlog.get_logger()


def prepare_extracted_files_v2(
    dossier: models.Dossier,
) -> Dict[str, schemas.ExtractedFileV2]:
    return {
        str(extracted_file["uuid"]): schemas.ExtractedFileV2.model_validate(
            {
                **extracted_file,
                "original_file_uuid": extracted_file["original_file__uuid"],
                "dossier_file_uuid": extracted_file["file__uuid"],
            }
        )
        for extracted_file in dossier.extractedfile_set.all().values(
            "uuid",
            "original_file__uuid",
            "path_from_original",
            "status",
            "updated_at",
            "created_at",
            "file__uuid",
        )
    }


def prepare_exception_data_for_api(exceptions: List[Tuple[str, Dict]]):
    return {
        file_path: {
            "uuid": exception["uuid"],
            "file_uuid": exception["extracted_file_id"],
            "dossier_file_uuid": exception["extracted_file__file__uuid"],
            "original_file_uuid": exception["extracted_file__original_file__uuid"],
            "type": exception["type"],
            "file_status": exception["extracted_file__status"],
            "id": exception["exception_type"],
            "path_from_original": models.DossierFile.objects.get(
                uuid=exception["extracted_file__original_file__file"]
            ).name,
            "last_update": exception["updated_at"],
            "created_at": exception["created_at"],
            "en": exception["en"],
            "de": exception["de"],
            "fr": exception["fr"],
            "it": exception["it"],
            "details": exception["details"] if ENABLE_ERROR_DETAIL_IN_BACKEND else None,
        }
        for file_path, exception in exceptions
    }


def prepare_processing_exceptions_v2(dossier: models.Dossier):
    exceptions = [
        (exception["extracted_file__path_from_original"], exception)
        for exception in models.FileException.objects.filter(
            type="Processed", dossier=dossier
        ).values(
            "uuid",
            "type",
            "exception_type",
            "extracted_file_id",
            "updated_at",
            "created_at",
            "extracted_file__path_from_original",
            "en",
            "de",
            "fr",
            "it",
            "details",
            "extracted_file__file__uuid",
            "extracted_file__original_file__uuid",
            "extracted_file__original_file__file",
            "extracted_file__status",
            "extracted_file__file__uuid",
            "extracted_file__original_file__uuid",
        )
    ]

    return prepare_exception_data_for_api(exceptions)


def processed_files_simple(dossier: models.Dossier):
    processed_files = ProcessedFile.objects.filter(dossier=dossier).select_related(
        "extracted_file",
        "extracted_file__original_file__file",
        "extracted_file__file",
        "extracted_file__original_file",
    )

    processed_pages = (
        ProcessedPage.objects.filter(processed_file__dossier=dossier)
        .select_related("image")
        .all()
    )

    return {
        str(processed_file.uuid): {
            "original_file_path": processed_file.extracted_file.original_file.file.name,
            "extracted_file_uuid": str(processed_file.extracted_file.file.uuid),
            "filename": processed_file.extracted_file.file.name,
            "pages": {
                str(processed_page.number): {
                    "image": processed_page.image.fast_url,
                }
                for processed_page in processed_pages
                if processed_page.processed_file_id == processed_file.uuid
            },
        }
        for processed_file in processed_files
    }


def default_sort(a, b):
    if a > b:
        return 1
    elif a == b:
        return 0
    else:
        return -1


def sort_by_document_category_id(a_id: int, b_id: int):
    min_valid_id = 200
    max_valid_id = 800

    if min_valid_id <= a_id < max_valid_id and min_valid_id <= b_id < max_valid_id:
        return 0 + default_sort(a_id, b_id)

    elif a_id < min_valid_id and b_id < min_valid_id:
        return 0 + default_sort(a_id, b_id)

    elif max_valid_id > a_id >= min_valid_id > b_id:
        return -1 - default_sort(a_id, b_id)

    elif a_id < min_valid_id < b_id < max_valid_id:
        return 1 - default_sort(a_id, b_id)

    elif a_id >= max_valid_id and b_id >= max_valid_id:
        return 0 + default_sort(a_id, b_id)

    elif a_id < min_valid_id and b_id >= max_valid_id:
        return 0 + default_sort(a_id, b_id)

    else:
        return 0 + default_sort(a_id, b_id)


def sort_document(a, b):
    a_id = a["document_category"]["id"]
    b_id = b["document_category"]["id"]

    a_title, a_status = check_page_object_title(a["formatted_title"], a_id)
    b_title, b_status = check_page_object_title(b["formatted_title"], b_id)

    return default_sort(a["formatted_title"], b["formatted_title"]) - 1


def get_qs_for_semantic_documents(
    dossier: models.Dossier,
    show_soft_deleted: bool = False,  # Show ONLY files that have been soft deleted
    hide_empty_semantic_documents: bool = True,
    date_range: DateRange = None,
    show_all_documents_for_soft_deleted: bool = False,
) -> models.QuerySet:
    """

    @param dossier:
    @param show_soft_deleted: If True show ONLY soft deleted documents and not non-deleted documents
    @param hide_empty_semantic_documents:
    @param date_range:
    @param show_all_documents_for_soft_deleted: If this is True AND show_soft_deleted is True then show all documents including soft deleted
    @return:
    """
    # If this is False and show_soft_deleted is True then show all documents and pages (deleted and not-deleted)
    # If this is True and show_soft_deleted is True then show only deleted documents and pages
    # Has no effect if show_soft_deleted is False

    manager = (
        semantic_document_models.SemanticDocument.all_objects
        if show_soft_deleted
        else semantic_document_models.SemanticDocument.objects
    )

    filter_semanticpages = Q(semantic_pages__isnull=False)
    filter_semantic_document = Q()
    if show_soft_deleted:
        if show_all_documents_for_soft_deleted:
            filter_semantic_document = None
        else:
            # Remove not-deleted documents and pages
            filter_semantic_document &= Q(deleted_at__isnull=False)
            filter_semantic_document |= Q(semantic_pages__deleted_at__isnull=False)
    else:
        filter_semanticpages &= Q(semantic_pages__deleted_at__isnull=True)

    semantic_documents_queryset = manager.annotate(
        semanticpages=ArrayAgg(
            "semantic_pages", distinct=True, filter=filter_semanticpages, default=None
        ),
        status_deleted=ExpressionWrapper(
            Q(deleted_at__isnull=False), output_field=BooleanField()
        ),
    ).filter(dossier=dossier)
    if filter_semantic_document:
        semantic_documents_queryset = semantic_documents_queryset.filter(
            filter_semantic_document
        )

    if date_range and (date_range.from_date and date_range.to_date):
        # change the string dates to datetime with timezone to allow filtering to minute and hour level
        from_date = datetime.strptime(
            date_range.from_date, "%Y-%m-%dT%H:%M:%S.%fZ"
        ).replace(tzinfo=timezone.utc)
        to_date = datetime.strptime(
            date_range.to_date, "%Y-%m-%dT%H:%M:%S.%fZ"
        ).replace(tzinfo=timezone.utc)

        # Add a filter for the inclusive date range on the updated_at field
        semantic_documents_queryset = semantic_documents_queryset.filter(
            updated_at__gte=from_date, updated_at__lte=to_date
        )

    if hide_empty_semantic_documents:
        empty_documents = (
            SemanticDocument.objects.filter(dossier=dossier)
            .annotate(
                page_count=Count(
                    "semantic_pages", filter=Q(semantic_pages__deleted_at__isnull=True)
                )
            )
            .filter(page_count=0)
            .values("uuid")
        )
        semantic_documents_queryset = semantic_documents_queryset.exclude(
            uuid__in=empty_documents
        )

    return semantic_documents_queryset


def serialize_semantic_page(semantic_page: dict, include_annotations: bool = True):
    # Convert user annotations to UserAnnotationsSchema format
    annotations = []
    if include_annotations and semantic_page.get("user_annotations"):
        for annotation in semantic_page["user_annotations"]:
            annotations.append(
                UserAnnotationsSchema(
                    annotation_group_uuid=annotation["annotation_group_uuid"],
                    annotation_type=annotation["annotation_type"],
                    text=annotation["text"],
                    bbox_top=annotation["bbox_top"],
                    bbox_left=annotation["bbox_left"],
                    bbox_width=annotation["bbox_width"],
                    bbox_height=annotation["bbox_height"],
                    hexcolor=annotation["hexcolor"] or "#FFFF00",
                )
            )

    return {
        "uuid": str(semantic_page["uuid"]),
        "status_deleted": semantic_page["status_deleted"],
        "lang": semantic_page["lang"],
        "source_file_uuid": str(semantic_page["processed_page__processed_file__uuid"]),
        "source_page_number": semantic_page["processed_page__number"],
        "number": semantic_page["number"],
        "page_category": {
            "id": semantic_page["page_category__id"],
            "name": semantic_page["page_category__name"],
        },
        "document_category": {
            "id": semantic_page["document_category__id"],
            "name": semantic_page["document_category__name"],
        },
        "confidence_summary": {
            "value": semantic_page["confidence_value"],
            "value_formatted": semantic_page["confidence_formatted"],
            "level": semantic_page["confidence_level"],
        },
        "confidence": semantic_page["confidence_value"],
        "rotation_angle": semantic_page["rotation_angle"],
        "page_objects": [],
        "annotations": annotations,  # Add annotations to the output
    }


def get_semantic_pages(
    dossier: UUID, show_soft_deleted: bool, include_annotations: bool = False
):
    manager = (
        semantic_document_models.SemanticPage.all_objects
        if show_soft_deleted
        else semantic_document_models.SemanticPage.objects
    )

    query = manager.filter(dossier=dossier).select_related(
        "processed_page__processed_file__extracted_file",
        "processed_page",
        "document_category",
        "page_category",
    )

    if include_annotations:
        query = query.prefetch_related("user_annotations")

    # Define base values to fetch
    values_list = [
        "semantic_document__uuid",
        "uuid",
        "status_deleted",
        "lang",
        "number",
        "confidence_value",
        "rotation_angle",
        "processed_page__number",
        "page_category__id",
        "page_category__name",
        "document_category__id",
        "document_category__name",
        "confidence_value",
        "confidence_formatted",
        "confidence_level",
        "processed_page__processed_file__uuid",
    ]

    pages = list(
        query.annotate(
            status_deleted=ExpressionWrapper(
                Q(deleted_at__isnull=False), output_field=BooleanField()
            ),
        )
        .order_by("semantic_document", "number", "deleted_at", "-updated_at")
        .values(*values_list)
    )

    if include_annotations:
        # Get all page UUIDs
        page_uuids = [page["uuid"] for page in pages]

        # Fetch annotations separately
        annotations_dict = {}
        annotations = SemanticPageUserAnnotations.objects.filter(
            semantic_page__uuid__in=page_uuids
        ).values(
            "semantic_page__uuid",
            "annotation_group_uuid",
            "annotation_type",
            "text",
            "bbox_top",
            "bbox_left",
            "bbox_width",
            "bbox_height",
            "hexcolor",
        )

        # Group annotations by page UUID
        for annotation in annotations:
            page_uuid = annotation["semantic_page__uuid"]
            if page_uuid not in annotations_dict:
                annotations_dict[page_uuid] = []
            annotations_dict[page_uuid].append(
                {
                    "annotation_group_uuid": annotation["annotation_group_uuid"],
                    "annotation_type": annotation["annotation_type"],
                    "text": annotation["text"],
                    "bbox_top": annotation["bbox_top"],
                    "bbox_left": annotation["bbox_left"],
                    "bbox_width": annotation["bbox_width"],
                    "bbox_height": annotation["bbox_height"],
                    "hexcolor": annotation["hexcolor"],
                }
            )

        # Add annotations to pages
        for page in pages:
            page["user_annotations"] = annotations_dict.get(page["uuid"], [])

    return pages


def prepare_semantic_documents_for_api_v2(
    dossier,
    include_annotations: bool,
    show_soft_deleted: bool = False,
    hide_empty_semantic_documents: bool = False,
    date_range: DateRange = None,
    enable_page_objects: bool = True,
):
    semantic_documents_values_list = [
        "uuid",
        "title_custom",
        "title_suffix",
        "confidence_formatted",
        "confidence_value",
        "confidence_level",
        "confidence_value",
        "document_category",
        "document_category__name",
        "document_category__id",
        "document_category__de",
        "document_category__en",
        "document_category__fr",
        "document_category__it",
        "document_category__de_external",
        "document_category__en_external",
        "document_category__fr_external",
        "document_category__it_external",
        "document_category__additional_search_terms_de",
        "document_category__additional_search_terms_en",
        "document_category__additional_search_terms_fr",
        "document_category__additional_search_terms_it",
        "deleted_at",
        "created_at",
        "updated_at",
        "access_mode",
        "work_status__key",
    ]

    semantic_documents_queryset = get_qs_for_semantic_documents(
        dossier=dossier,
        show_soft_deleted=show_soft_deleted,
        hide_empty_semantic_documents=hide_empty_semantic_documents,
        date_range=date_range,
    )

    document_categories = get_document_categories_by_uuid(dossier.account)

    key_for_sorting = cmp_to_key(sort_document)

    semantic_pages_objects = get_semantic_pages_object(dossier)

    semantic_pages = get_semantic_pages(
        dossier, show_soft_deleted, include_annotations=include_annotations
    )

    semantic_documents = semantic_documents_queryset.values(
        *semantic_documents_values_list
    )

    simple_data = [
        {
            "uuid": semantic_document["uuid"],
            "title_custom": semantic_document["title_custom"],
            "document_category_translated": document_categories[
                semantic_document["document_category"]
            ].translated(dossier.lang.lower()),
            "formatted_title": semantic_document_models.SemanticDocument.calculate_title(
                dossier.lang.lower(),
                semantic_document["title_custom"],
                document_categories[semantic_document["document_category"]],
                semantic_document["title_suffix"],
            ),
            # Do not add filename here as that needs to be created to be unique
            "suffix": semantic_document["title_suffix"],
            "status_deleted": (
                False if semantic_document["deleted_at"] is None else True
            ),
            "confidence_summary": helpers_v1.get_confidence_summary(semantic_document),
            "confidence": semantic_document["confidence_value"],
            "document_category": map_document_category(semantic_document),
            "work_status": semantic_document["work_status__key"],
            "aggregated_objects": (
                []
                if not enable_page_objects
                else [
                    *get_serialized_pages_objects(
                        semantic_pages_objects,
                        [
                            semantic_page["uuid"]
                            for semantic_page in semantic_pages
                            if semantic_page["semantic_document__uuid"]
                            == semantic_document["uuid"]
                        ],
                        True,
                    )
                ]
            ),  # this is replaced further down as it does not make sense
            "semantic_pages": [
                {
                    **serialize_semantic_page(
                        semantic_page, include_annotations=include_annotations
                    ),
                    "page_objects": (
                        []
                        if not enable_page_objects
                        else get_serialized_pages_objects(
                            semantic_pages_objects, [semantic_page["uuid"]], False
                        )
                    ),
                }
                for semantic_page in semantic_pages
                if semantic_page["semantic_document__uuid"] == semantic_document["uuid"]
            ],
            "created_at": semantic_document["created_at"],
            "updated_at": semantic_document["updated_at"],
            "access_mode": semantic_document["access_mode"],
        }
        for semantic_document in semantic_documents
    ]

    # List of Dict. Each Dict contains as keys semantic_documents_values_list and as values the properties of the
    # dossier
    sorted_data = sorted(simple_data, key=key_for_sorting)

    # updated_data = generate_page_objects(
    #     dossier.uuid, sorted_data, "aggregated_objects", True
    # )
    # updated_data = generate_semantic_pages(
    #     dossier.uuid, updated_data, show_soft_deleted
    # )

    updated_data = create_filenames_unique(sorted_data)

    return updated_data


def map_document_category(semantic_document):
    return {
        "name": semantic_document["document_category__name"],
        "id": semantic_document["document_category__id"],
        "de": semantic_document["document_category__de"],
        "en": semantic_document["document_category__en"],
        "fr": semantic_document["document_category__fr"],
        "it": semantic_document["document_category__it"],
        "de_external": semantic_document["document_category__de_external"],
        "en_external": semantic_document["document_category__en_external"],
        "fr_external": semantic_document["document_category__fr_external"],
        "it_external": semantic_document["document_category__it_external"],
        "additional_search_terms_de": semantic_document[
            "document_category__additional_search_terms_de"
        ],
        "additional_search_terms_en": semantic_document[
            "document_category__additional_search_terms_en"
        ],
        "additional_search_terms_fr": semantic_document[
            "document_category__additional_search_terms_fr"
        ],
        "additional_search_terms_it": semantic_document[
            "document_category__additional_search_terms_it"
        ],
    }


def get_businesscases_by_business_parkey(account: Account, business_partner: Partner):
    instance_type = get_bekb_instance_type(account.key)
    if has_businesscases(instance_type):
        business_cases = BusinessCase.objects.filter(business_partner=business_partner)
        business_cases_list = list(business_cases)
        return business_cases_list
    else:
        # This is the FIPLA instance type
        return list()


def prepare_semantic_dossier_v2(
    dossier: models.Dossier,
    show_soft_deleted: bool = False,
    hide_empty_semantic_documents: bool = False,
    show_count_deleted_objects: bool = False,
    enable_bekb_export: bool = False,
    enable_page_objects: bool = True,
    include_annotations: bool = True,
):
    dossier_data = helpers_v1.prepare_for_api_dossier_data(
        dossier, show_count_deleted_objects
    )
    extracted_files_v2 = prepare_extracted_files_v2(dossier)
    extracted_files = helpers_v1.prepare_extracted_files_data(dossier)
    processing_exceptions = prepare_processing_exceptions_v2(dossier)
    semantic_documents = prepare_semantic_documents_for_api_v2(
        dossier,
        show_soft_deleted=show_soft_deleted,
        hide_empty_semantic_documents=hide_empty_semantic_documents,
        enable_page_objects=enable_page_objects,
        include_annotations=include_annotations,
    )
    processed_files = processed_files_simple(dossier)
    bekb = None

    bekb_dossier_properties_list = BEKBDossierProperties.objects.filter(dossier=dossier)

    if len(bekb_dossier_properties_list) > 0 and enable_bekb_export:
        bekb_dossier_properties = bekb_dossier_properties_list[0]

        business_partner = bekb_dossier_properties.business_partner
        partner_partner = bekb_dossier_properties.partner_partner
        business_case = bekb_dossier_properties.business_case

        business_cases_list = get_businesscases_by_business_parkey(
            dossier.account, business_partner
        )

        # semantic_documents_in_bekb_dossier_properties = (
        #     bekb_dossier_properties.dossier.semantic_documents.all()
        # )
        # CollateralAssignment.objects.filter(
        #     semantic_document__uuid__in=semantic_documents_in_bekb_dossier_properties
        # )

        prepared_collaterals = prepare_bekb_collaterals(
            business_case, business_partner, dossier
        )

        bekb_business_case = prepare_bekb_business_case(business_case, dossier)

        bekb = {
            "business_partner": schemasBekb.Partner(
                parkey=business_partner.parkey,
                name=business_partner.name,
                firstname=business_partner.firstname,
            ),
            "partner_partner": (
                schemasBekb.Partner(
                    parkey=partner_partner.parkey,
                    name=partner_partner.name,
                    firstname=partner_partner.firstname,
                )
                if partner_partner
                else None
            ),
            "business_case": bekb_business_case,
            "business_cases": [
                prepare_bekb_business_case(item, dossier)
                # schemas.BusinessCase(
                #     business_parkey=item.business_partner.parkey,
                #     business_number=item.business_number,
                #     business_type=Attribute.objects.filter(
                #         account=dossier.account,
                #         entity=Attribute.Entity.BusinessType.value,
                #         key=item.business_type.key,
                #     )
                #     .first()
                #     .__dict__,
                #     business_status=Attribute.objects.filter(
                #         account=dossier.account,
                #         entity=Attribute.Entity.BusinessStatus.value,
                #         key=item.business_status.key,
                #     )
                #     .first()
                #     .__dict__,
                #     mutation_date=item.mutation_date,
                #     mutation_user=item.mutation_user,
                # )
                for item in business_cases_list
            ],
            "collaterals": prepared_collaterals,
        }

    return {
        "dossier_data": dossier_data,
        "extracted_files_v2": extracted_files_v2,
        "extracted_files": extracted_files,
        "processing_exceptions": processing_exceptions,
        "processed_files": processed_files,
        "semantic_documents": semantic_documents,
        "bekb": bekb,
    }


def prepare_bekb_business_case(
    business_case: BusinessCase, dossier: Dossier
) -> schemas.BusinessCase:
    bekb_business_case = None
    if business_case:
        business_type = Attribute.objects.filter(
            account=dossier.account,
            entity=Attribute.Entity.BusinessType.value,
            key=business_case.business_type.key,
        ).first()

        business_status = Attribute.objects.filter(
            account=dossier.account,
            entity=Attribute.Entity.BusinessStatus.value,
            key=business_case.business_status.key,
        ).first()

        bekb_business_case = schemas.BusinessCase(
            business_parkey=business_case.business_partner.parkey,
            business_number=business_case.business_number,
            business_type=business_type.__dict__,
            business_status=business_status.__dict__,
            mutation_date=business_case.mutation_date,
            mutation_user=business_case.mutation_user,
        )
    return bekb_business_case


def prepare_bekb_collaterals(
    business_case: BusinessCase, business_partner: Partner, dossier: Dossier
) -> List[CollateralWithAssignments]:
    prepared_collaterals = []
    collaterals = get_collaterals_by_businesscase(
        account=dossier.account,
        business_partner=business_partner,
        business_case=business_case,
    )
    for item in collaterals:
        collateral = prepare_collateral(item)
        prepared_collaterals.append(collateral)
    return prepared_collaterals


def prepare_semantic_documents_status(dossier, semantic_documents):
    bekb_dossier_properties = BEKBDossierProperties.objects.filter(
        dossier=dossier
    ).first()

    semantic_documents_status = []

    if bekb_dossier_properties is not None:
        requirements_satisfaction_options = (
            get_collateral_requirements_satisfaction_options_for_account(
                dossier.account
            )
        )

        all_collaterals_valid = get_collaterals_by_businesscase(
            account=bekb_dossier_properties.account,
            business_partner=bekb_dossier_properties.business_partner,
            business_case=bekb_dossier_properties.business_case,
        )

        for semantic_document in semantic_documents:
            semantic_document_status = collateral_assignment_status(
                all_collaterals_valid,
                semantic_document,
                requirements_satisfaction_options,
            ).model_dump()

            semantic_documents_status.append(semantic_document_status)

    return semantic_documents_status


def add_dossier_roles_in_dossier(dossiers_list):
    data_list = []
    for dossier in dossiers_list:
        user_involvements = dossier.userinvolvement_set.all()

        def sort_dossier_roles_Fico(a, b):
            return 1 if b.role.key == "FICO" else -1

        key_for_sorting = cmp_to_key(sort_dossier_roles_Fico)

        user_involvements = sorted(user_involvements, key=key_for_sorting)

        added_roles = {
            **dossier.__dict__,
            "dossier_roles": [
                schemas.DossierRole(
                    uuid=user_involvement.role.uuid,
                    account=user_involvement.role.account.name,
                    key=user_involvement.role.key,
                    name_de=user_involvement.role.name_de,
                    name_en=user_involvement.role.name_en,
                    name_fr=user_involvement.role.name_fr,
                    name_it=user_involvement.role.name_it,
                    user_selectable=user_involvement.role.user_selectable,
                    show_separate_filter=user_involvement.role.show_separate_filter,
                    users=[
                        {
                            "first_name": user_involvement.user.user.first_name,
                            "last_name": user_involvement.user.user.last_name,
                            "username": user_involvement.user.user.username,
                        }
                    ],
                )
                for user_involvement in user_involvements
            ],
        }
        data_list.append(added_roles)

    return data_list


def get_dossier_roles(dossier: Dossier) -> List[schemas.DossierRole]:
    user_involvements = dossier.userinvolvement_set.all()

    def sort_dossier_roles_Fico(a, b):
        return 1 if b.role.key == "FICO" else -1

    key_for_sorting = cmp_to_key(sort_dossier_roles_Fico)

    user_involvements = sorted(user_involvements, key=key_for_sorting)

    return [
        schemas.DossierRole(
            uuid=user_involvement.role.uuid,
            account=user_involvement.role.account.name,
            key=user_involvement.role.key,
            name_de=user_involvement.role.name_de,
            name_en=user_involvement.role.name_en,
            name_fr=user_involvement.role.name_fr,
            name_it=user_involvement.role.name_it,
            user_selectable=user_involvement.role.user_selectable,
            show_separate_filter=user_involvement.role.show_separate_filter,
            users=[
                {
                    "first_name": user_involvement.user.user.first_name,
                    "last_name": user_involvement.user.user.last_name,
                    "username": user_involvement.user.user.username,
                }
            ],
        )
        for user_involvement in user_involvements
    ]


def get_bekb_dossier_properties(dossier: Dossier):
    if (
        hasattr(dossier, "bekbdossierproperties")
        and dossier.bekbdossierproperties is not None
    ):
        dossier_properties = dossier.bekbdossierproperties
        return schemas.DossierBekbProperties(
            partner=dossier_properties.business_partner,
            business_number=getattr(
                dossier_properties.business_case, "business_number", ""
            ),
            pers=dossier_properties.pers,
        )
    return None


def add_search_to_query_set(query_set: QuerySet, search_value: str):
    if search_value:
        qs = query_set.filter(
            Q(name__icontains=search_value)
            | Q(external_id__icontains=search_value)
            | Q(bekbdossierproperties__business_partner__parkey__icontains=search_value)
        )
    else:
        qs = query_set
    return qs


def filter_and_sort_dossiers(
    query_set,
    type_ordering,
    search_value,
    ordering_value,
    filter_businesscase_type,
    filter_dossier_role,
    filter_business_partner_type,
    filter_status_type,
    filter_user_involvment_usernames,
    is_manager,
    username,
    account_key,
):
    """
    Example request:

    https://dms.hypo-staging-du.duckdns.org/api/dossier/?page_size=25&number_page=1&ordering_value=created_at&type_ordering=desc&
        filter_businesscase_type=fb827d5d-8c1d-4c75-9bc6-1611bf793796,65f4994b-6b59-44ba-b74f-7427cc9cdcdc,no-status&
        filter_dossier_role=ce87e53f-3024-4edf-b4de-b6446eb2d24f,3c182bce-025d-44e4-a225-93294ae890b0&
        filter_status_type=c99124af-6de4-4c27-9672-414db41fc8b6,cadb4e59-30b6-476a-bc81-ee62ec9dd4c6,c2e7a13e-5fb7-4fd9-a912-f06c92aeabf5,ac08b192-d2ce-4d1b-830e-34e853b5847e&
        filter_owner_username=bekb1%40hypodossier.ch,hunzikerarlette%40bekbe.ch,bekb1%40hypodossier.ch,bekb1%40hypodossier.ch,no-status

    @param query_set:
    @param type_ordering: 'desc' or 'asc'
    @param search_value: Term from search field to be searched in dossier name and business parkey for bekb
    @param ordering_value: any of this, default is expiry_date: ['name', 'count_documents', 'created_at', 'expiry_date', 'status']
    @param filter_businesscase_type: list of uuid (and string 'no-status') for entries of business case type that apply
    @param filter_dossier_role: list of uuid of user roles (used in UserInvolvment), to be used together with filter_owner_username (TODO: this needs a fix, see below)
    @param filter_business_partner_type:
    @param filter_status_type: list of uuid for dossier status (and string 'no-status'
    @param filter_user_involvment_usernames: List of username (email) of dossier users that are related to a dossier in any of the rolfes from filter_dossier_role (TODO: this needs a fix, see below)
    @param is_manager:
    @param username:
    @param account_key:
    @return:
    """
    _filters = []

    if is_manager:
        query_set = query_set.annotate(
            owner_username=F("owner__username"),
            owner_first_name=F("owner__first_name"),
            owner_last_name=F("owner__last_name"),
        ).filter(account__key=account_key)
    else:
        query_set = query_set.filter(account__key=account_key).filter(
            owner__username=username
        )

    query_set = add_search_to_query_set(query_set, search_value)

    filter_bct = create_filter_businesscase_type(filter_businesscase_type)
    if filter_bct:
        _filters.append(filter_bct)

    if filter_status_type:
        status_list = filter_status_type.split(",")
        filter_status = Q(work_status_id__in=status_list)
        if "no-status" in status_list:
            status_list = list(filter(lambda x: x != "no-status", status_list))
            filter_status = Q(work_status_id__in=status_list) | Q(
                work_status_id__isnull=True
            )
        _filters.append(filter_status)

    # TODO: dossier_uuid_list can get very long (and gets longer if a user has more dossiers)
    # TODO: logic is not correct for >1 entries in filter_dossier_role_list as a it combines all roles and valid values
    # in one query.
    # Note that there is a restriction to "select all" in the frontend (e.g. 20 entries) so a user will not e.g. select 400 entries manually.
    if filter_user_involvment_usernames and filter_dossier_role:
        filter_dossier_role_list = filter_dossier_role.split(",")
        filter_dossier_owner_username = filter_user_involvment_usernames.split(",")

        user_involvements = models.UserInvolvement.objects.filter(
            # only fetch involvements for non-deleted dossiers
            dossier__expiry_date__gt=django.utils.timezone.now(),
            role__in=filter_dossier_role_list,
            user__user__username__in=filter_dossier_owner_username,
        )

        # this is the slower (about 1.5x as long) query
        # dossier_uuid_list = [i.dossier.uuid for i in user_involvements]

        # this is the faster query
        dossier_uuid_list = user_involvements.values_list("dossier__uuid", flat=True)

        # num_uuids2 = len(dossier_uuid_list)
        # logger.info(f"Found {num_uuids2} dossier_uuid_list")

        filter_role = Q(**{"uuid__in": dossier_uuid_list})
        _filters.append(filter_role)

    if filter_business_partner_type:
        business_partner_id_list = filter_business_partner_type.split(",")
        query_set = query_set.filter(
            bekbdossierproperties__business_partner__uuid__in=business_partner_id_list
        )

    query_set = query_set.filter(*_filters)

    ordering_value = helpers_v1.get_ordering_value_to_dossier_list(
        ordering_value, type_ordering
    )
    query_set = query_set.order_by(ordering_value)

    query_set = query_set.prefetch_related(
        "userinvolvement_set__role",
        "userinvolvement_set__user__user",
        "userinvolvement_set__role__account",
        "bekbdossierproperties__business_case",
    )

    return query_set


def create_filter_businesscase_type(filter_businesscase_type: Optional[str]):
    if filter_businesscase_type:
        filter_businesscase_type_list = filter_businesscase_type.split(",")
        filter_businesscase = Q(businesscase_type__in=filter_businesscase_type_list)
        if "no-status" in filter_businesscase_type_list:
            filter_businesscase_type_list = list(
                filter(lambda x: x != "no-status", filter_businesscase_type_list)
            )
            filter_businesscase = Q(
                businesscase_type__in=filter_businesscase_type_list
            ) | Q(businesscase_type__isnull=True)
        return filter_businesscase
    return None


def prepare_semantic_dossier(
    dossier,
    show_soft_deleted: bool = False,
    show_count_deleted_objects: bool = False,
    hide_empty_semantic_documents: bool = False,
    date_range: DateRange = None,
    enable_page_objects: bool = True,
    include_annotations: bool = True,
) -> SemanticDossier:
    original_files = prepare_original_files(dossier)
    dossier_files = prepare_dossier_files(dossier)
    extracted_files = prepare_extracted_files_data(dossier)
    extracted_files_v2 = prepare_extracted_files_v2(dossier)
    processing_exceptions = prepare_processing_exceptions_v2(dossier)

    processed_files = prepare_processed_files_for_api(dossier)

    semantic_documents = prepare_semantic_documents_for_api_v2(
        dossier,
        show_soft_deleted=show_soft_deleted,
        hide_empty_semantic_documents=hide_empty_semantic_documents,
        date_range=date_range,
        enable_page_objects=enable_page_objects,
        include_annotations=include_annotations,
    )

    dossier = annotate_with_calculated_access_mode_dossier(
        Dossier.objects.filter(uuid=dossier.uuid)
    ).first()

    dossier_data = prepare_for_api_dossier_data(dossier, show_count_deleted_objects)
    return SemanticDossier(
        **dossier_data,
        original_files=original_files,
        dossier_files=dossier_files,
        extracted_files_v2=extracted_files_v2,
        extracted_files=extracted_files,
        processing_exceptions=processing_exceptions,
        processed_files=processed_files,
        semantic_documents=semantic_documents,
    )


def sort_semantic_page(a, b):
    a_id = a.number
    b_id = b.number
    return default_sort(a_id, b_id) - 1


def create_pdf_from_semantic_document(
    semantic_document: SemanticDocument,
) -> pypdf.PdfWriter:
    writer = pypdf.PdfWriter()

    key_for_sorting = cmp_to_key(sort_semantic_page)

    sorted_semantic_pages = sorted(
        semantic_document.semantic_pages.all(), key=key_for_sorting
    )

    for semantic_page in sorted_semantic_pages:
        pdf_page_url = semantic_page.processed_page.searchable_pdf.fast_url
        pdf_reader = pypdf.PdfReader(BytesIO(requests.get(pdf_page_url).content))

        writer.add_page(rotate_pdf(pdf_reader.pages[0], semantic_page.rotation_angle))

        writer.add_metadata(
            {
                "/Author": "HypoDossier",
                "/Creator": "HypoDossier",
                "/Producer": "HypoDossier",
                "/NeedAppearances": "",
            }
        )

    return writer


def generate_list_delegate(delegate):
    return [
        schemas.Delegation(
            uuid=i.uuid,
            user=schemas.AccessDelegationAccount(
                firstname=i.delegator.first_name,
                lastname=i.delegator.last_name,
                username=i.delegator.username,
            ),
            expiry_time=i.expire_time,
        )
        for i in delegate
    ]


def generate_list_delegator(delegator):
    return [
        schemas.Delegation(
            uuid=i.uuid,
            user=schemas.AccessDelegationAccount(
                firstname=i.delegate.first_name,
                lastname=i.delegate.last_name,
                username=i.delegate.username,
            ),
            expiry_time=i.expire_time,
        )
        for i in delegator
    ]


def generate_list_users_on_same_account(users_on_same_account):
    return [
        schemas.AccessDelegationAccount(
            firstname=i.user.first_name,
            lastname=i.user.last_name,
            username=i.user.username,
        )
        for i in users_on_same_account
    ]


def check_enable_dossier_permission(account):
    return account.enable_dossier_permission


def is_valid_uuid(value):
    try:
        uuid.UUID(value, version=4)
        return True
    except ValueError:
        return False
