import random

import djclick as click
import structlog
from django.contrib.auth import get_user_model

from clientis.schemas.schemas import (
    ClientisExternalId,
)
from dossier.helpers_model_copier import copy_dossier_models
from dossier.models import Account, Dossier
from semantic_document.models import SemanticDocument

User = get_user_model()
logger = structlog.get_logger()


def generate_random_clientis_external_id() -> str:
    """
    Generates a random external ID following the format: BankNR.KundenNR.RahmenNR.AntragsNR
    Example: 949.401678.001.110794
    """
    # Bank number (typically 3 digits)
    bank_nr = str(random.randint(100, 999))

    # Client number (typically 6 digits)
    client_nr = str(random.randint(100000, 999999))

    # Frame contract number (typically 3 digits with leading zeros)
    frame_nr = str(random.randint(1, 999)).zfill(3)

    # Application number (typically 6 digits)
    application_nr = str(random.randint(100000, 999999))

    # Combine all parts with dots
    external_id = f"{bank_nr}.{client_nr}.{frame_nr}.{application_nr}"

    # Verify that the generated ID is valid
    ClientisExternalId(external_id=external_id)

    return external_id


@click.group()
def grp():
    pass


@grp.command()
@click.argument("account_key", required=True)
@click.argument("external_id", required=False, default=None)
@click.argument("name", required=False, default="finhurdle samples")
def duplicate_sample_dossier_to_account(
    account_key: str, external_id: str = None, name: str = "finhurdle samples"
):
    """
    Duplicates a sample dossier to a specific account.

    This command now properly handles document category mapping between accounts.

    python manage.py duplicate_sample_dossier_to_account duplicate-sample-dossier-to-account clientistest

    """
    target_account = Account.objects.filter(key=account_key).first()

    assert target_account, f"Account with key '{account_key}' not found"

    # Take the oldest dossier with that name, do not fail if multiple exist
    old_dossier = (
        Dossier.objects.filter(
            name=name,
            account=Account.objects.get(key="default"),
        )
        .order_by("created_at")
        .first()
    )

    assert (
        old_dossier
    ), "Sample dossier 'finhurdle samples' not found in default account"

    if not external_id:
        external_id = generate_random_clientis_external_id()
        logger.info(f"Generated external_id for target dossier: {external_id}")

    # Copy dossier with proper account handling - category mapping happens automatically
    copy_dossier_models(
        dossier=old_dossier,
        external_id=external_id,
        new_dossier_account=target_account,
    )

    dossier = Dossier.objects.get(external_id=external_id)
    dossier.save()

    # Clean up all the title_custom (legacy attribute). Otherwise the translations of the document title
    # do not show up because title_custom will be applied for all langs
    semdocs = SemanticDocument.objects.filter(dossier=dossier).all()
    for s in semdocs:
        s.title_custom = None
        s.save()

    # Verify and report category mappings
    semdocs_with_missing_categories = SemanticDocument.objects.filter(
        dossier=dossier, document_category__isnull=True
    ).count()

    if semdocs_with_missing_categories > 0:
        logger.warning(
            f"{semdocs_with_missing_categories} semantic documents have no document category (likely due to missing category mappings)"
        )

    logger.info(
        f"Successfully copied dossier '{old_dossier.name}' to account '{account_key}' with external_id '{external_id}' and new name '{dossier.name}'"
    )
