from datetime import datetime, date
from enum import Enum
from typing import Optional, List, Dict, Any
from uuid import UUID, uuid4

from pydantic import (
    field_validator,
    StringConstraints,
    ConfigDict,
    BaseModel,
    RootModel,
    create_model,
)

from core.schema import PaginatedData
from doccheck.models import DocCheck
from dossier.models import (
    ExportStrategy,
    NavigationStrategy,
    DossierAccessCheckProvider,
    DossierAccessCheckErrorComponent,
)
from events.schemas import EventDetail
from projectconfig.jwk import KeyPair
from semantic_document.schemas_page_annotations import UserAnnotationsSchema
from statemgmt.models import StateMachine
from typing_extensions import Annotated

DossierName = Annotated[str, StringConstraints(max_length=255)]


class DossierListAccessDecision(BaseModel):
    access: bool


class BusinessCaseType(BaseModel):
    uuid: UUID
    key: str
    name: Optional[str] = None
    description: Optional[str] = None


class Partner(BaseModel):
    uuid: UUID
    created_at: datetime
    updated_at: datetime
    parkey: str
    name: str
    firstname: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


Parkey = Annotated[str, StringConstraints(max_length=20)]
BusinessNumber = Annotated[str, StringConstraints(max_length=20)]
AttributeKey = Annotated[str, StringConstraints(max_length=40)]


class DossierBekbBusinessCaseRequestBody(BaseModel):
    business_number: Optional[str] = None


class BusinessCaseTypeLoad(BaseModel):
    account_key: str
    key: str
    name_de: Optional[str] = None
    description_de: Optional[str] = None
    name_en: Optional[str] = None
    description_en: Optional[str] = None
    name_fr: Optional[str] = None
    description_fr: Optional[str] = None
    name_it: Optional[str] = None
    description_it: Optional[str] = None
    order: Optional[int] = None


class BusinessCaseTypeExport(RootModel):
    root: List[BusinessCaseTypeLoad]


class DossierRole(BaseModel):
    uuid: UUID
    account: str
    key: str
    name_de: Optional[str] = None
    name_en: Optional[str] = None
    name_fr: Optional[str] = None
    name_it: Optional[str] = None
    user_selectable: bool
    show_separate_filter: bool
    users: Optional[List[dict]] = None


class DossierBekbProperties(BaseModel):
    partner: Optional[Partner] = None
    business_number: Optional[str] = None
    pers: Optional[bool] = False

    model_config = ConfigDict(from_attributes=True)


class Dossier(BaseModel):
    uuid: UUID
    name: Optional[str] = None
    creation: datetime
    updated_at: datetime
    count_documents: int
    status: str
    lang: str
    note: Optional[str] = None
    expiry_date: Optional[datetime] = None
    max_expiry_date: Optional[datetime] = None
    owner_username: Optional[str] = None
    owner_first_name: Optional[str] = None
    owner_last_name: Optional[str] = None
    businesscase_type_id: Optional[UUID] = None
    access_mode: str
    work_status_id: Optional[UUID] = None
    doccheck_case_id: Optional[UUID] = None
    dossier_roles: Optional[List[DossierRole]] = []
    bekb_dossier_properties: Optional[DossierBekbProperties] = None


class DossierCopyResponse(BaseModel):
    dossier_uuid: UUID

    updated_at: datetime
    created_at: datetime


class PaginatedDossier(PaginatedData):
    data: List[Dossier]
    total_dossiers_count: int


class CreatedObjectReference(BaseModel):
    uuid: UUID
    model_config = ConfigDict(from_attributes=True)


class CreateDossier(BaseModel):
    name: Optional[str] = None
    businesscase_type_id: Optional[str] = None
    lang: str


class CopyDossier(BaseModel):
    uuid: UUID


class Entity(str, Enum):
    BusinessStatus = "BusinessStatus"
    BusinessType = "BusinessType"
    CollateralType = "CollateralType"
    CollateralStatus = "CollateralStatus"
    PropertyType = "PropertyType"
    PropertyCollateralType = "PropertyCollateralType"
    RealEstatePropertyStatus = "RealEstatePropertyStatus"


class Attribute(BaseModel):
    entity: Entity
    key: AttributeKey
    name_de: Annotated[str, StringConstraints(max_length=255)]
    name_fr: Annotated[str, StringConstraints(max_length=255)]


class BusinessCase(BaseModel):
    """Geschäftsfall"""

    business_parkey: Parkey

    business_number: BusinessNumber
    business_type: Attribute
    business_status: Attribute
    mutation_date: date
    mutation_user: Annotated[str, StringConstraints(max_length=50)]


class ChangeDossierWorkStatus(BaseModel):
    work_status_id: UUID


class OriginalFileStatus(BaseModel):
    status: str
    data: str


class Exceptions(BaseModel):
    id: int
    en: str
    de: str
    fr: str
    it: str
    details: Optional[str] = None


class FileExceptionType(str, Enum):
    PROCESSED = "Processed"
    EXTRACTED = "Extracted"


class FileException(BaseModel):
    file_uuid: UUID
    dossier_file_uuid: Optional[UUID] = None
    type: FileExceptionType
    de: Optional[str] = None
    en: Optional[str] = None
    fr: Optional[str] = None
    it: Optional[str] = None
    details: Optional[str] = None
    file_status: Optional[str] = None
    uuid: Optional[UUID] = None
    original_file_uuid: Optional[UUID] = None
    path_from_original: Optional[str] = None
    last_update: Optional[datetime] = None
    created_at: Optional[datetime] = None


class ExtractedFile(BaseModel):
    original_file: str
    dossier_file_uuid: Optional[UUID] = None
    extracted_files: List[str]
    exceptions: Dict[str, FileException]
    status: str
    last_update: datetime
    created_at: datetime
    original_file_uuid: UUID
    original_file_name: Optional[str] = None


class ExtractedFileV2(BaseModel):
    uuid: UUID
    dossier_file_uuid: Optional[UUID] = None
    original_file_uuid: UUID
    path_from_original: str
    status: str
    updated_at: Optional[datetime] = None
    created_at: Optional[datetime] = None

    model_config = ConfigDict(str_strip_whitespace=True, from_attributes=True)


class Category(BaseModel):
    id: str
    name: str
    de: Optional[str] = None
    en: Optional[str] = None
    fr: Optional[str] = None
    it: Optional[str] = None
    de_external: Optional[str] = None
    en_external: Optional[str] = None
    fr_external: Optional[str] = None
    it_external: Optional[str] = None
    additional_search_terms_de: Optional[str] = None
    additional_search_terms_en: Optional[str] = None
    additional_search_terms_fr: Optional[str] = None
    additional_search_terms_it: Optional[str] = None


class BBox(BaseModel):
    ref_width: int
    ref_height: int
    top: int
    left: int
    right: int
    bottom: int


class ConfidenceSummary(BaseModel):
    value_formatted: str
    value: float
    level: str  # INSTEAD OF COLOR

    model_config = ConfigDict(from_attributes=True)

    @staticmethod
    def map_percentage_to_traffic_light_color(value):
        if value >= 0.9:
            return "high"
        if value >= 0.75:
            return "medium"
        return "low"

    @staticmethod
    def from_value(value):
        return ConfidenceSummary(
            value=value,
            value_formatted=f"{value:.0%}",
            level=ConfidenceSummary.map_percentage_to_traffic_light_color(value),
        )


class PageObjectTitles(BaseModel):
    model_config = ConfigDict(frozen=True, from_attributes=True)

    de: str
    en: str
    fr: str
    it: str


class PageObjectFullApiData(BaseModel):
    uuid: Optional[str] = None
    key: str
    title: str
    titles: PageObjectTitles
    visible: Optional[bool] = True
    value: Optional[str] = None
    type: str
    bbox: BBox
    page_number: int
    confidence: float
    confidence_summary: ConfidenceSummary
    semantic_page_uuid: Optional[str] = None
    annotations: Optional[List[UserAnnotationsSchema]] = None

    model_config = ConfigDict(from_attributes=True)


class PageObjectApiDataWithUUID(PageObjectFullApiData):
    uuid: UUID
    semantic_page_uuid: UUID

    model_config = ConfigDict(from_attributes=True)


class Classification(BaseModel):
    classification: str
    confidence: float
    classifier: str


class PageConfidenceInfo(BaseModel):
    parsername: str
    spacy_classifications: List[Classification]


class ProcessedPageFullApiData(BaseModel):
    number: int
    lang: str
    document_category: Category
    page_category: Category
    confidence: float
    confidence_info: Optional[PageConfidenceInfo] = None
    page_objects: Optional[List[PageObjectFullApiData]] = None
    image: Optional[str] = None
    searchable_pdf: Optional[str] = None
    searchable_txt: Optional[str] = None

    image_dossier_file_uuid: UUID
    searchable_pdf_dossier_file_uuid: UUID
    searchable_txt_dossier_file_uuid: UUID


class ProcessedFileFullApiData(BaseModel):
    original_file_path: str
    file_path: str
    file_path_url_encoded: str
    filename: str
    pages: Dict[str, ProcessedPageFullApiData]
    classifications: Dict[str, List[Classification]] = {}


class SemanticPageNoPageObjects(BaseModel):
    lang: str
    uuid: Optional[str] = None
    status_deleted: Optional[bool] = None
    source_file_uuid: str
    source_page_number: int
    rotation_angle: Optional[int] = None
    number: Optional[int] = None
    page_category: Category
    document_category: Category
    confidence_summary: ConfidenceSummary
    confidence: float
    confidence_info: Optional[PageConfidenceInfo] = None

    searchable_pdf: Optional[str] = None
    searchable_txt: Optional[str] = None


class SemanticPageFullApiData(SemanticPageNoPageObjects):
    page_objects: Optional[List[PageObjectFullApiData]] = []
    annotations: Optional[List[UserAnnotationsSchema]] = None


class DocumentConfidenceInfo(BaseModel):
    page_confidences: List[float]


class AccessMode(str, Enum):
    """
    Access mode of a semantic document (not the same as access mode of a dossier)
    """

    READ_ONLY = "read_only"
    READ_WRITE = "read_write"


class SemanticDocumentFullApiData(BaseModel):
    uuid: UUID
    status_deleted: Optional[bool] = None
    updated_at: datetime
    semantic_pages: List[SemanticPageFullApiData]
    document_category: Category
    aggregated_objects: Optional[List[PageObjectFullApiData]] = []

    filename: str
    suffix: Optional[str] = None
    title_elements: Optional[List[str]] = None
    confidence_summary: ConfidenceSummary
    confidence_info: Optional[DocumentConfidenceInfo] = None
    confidence: float

    formatted_title: str
    document_category_translated: Optional[str] = None
    title_custom: Optional[str] = None
    access_mode: AccessMode
    work_status: Optional[str] = None


class SemanticDocumentFullApiDataCreatedAt(SemanticDocumentFullApiData):
    created_at: datetime


class Redirect(BaseModel):
    location: str


class DossierFile(BaseModel):
    uuid: UUID
    url: str


class OriginalFile(BaseModel):
    uuid: UUID
    dossier_file_uuid: UUID

    model_config = ConfigDict(str_strip_whitespace=True, from_attributes=True)


class ProcessedPageSimple(BaseModel):
    image: str


class ProcessedFileSimple(BaseModel):
    original_file_path: str
    extracted_file_uuid: str
    filename: str
    pages: Dict[str, ProcessedPageSimple]


class SemanticDossierDefault(BaseModel):
    uuid: str
    note: Optional[str] = None
    name: Optional[str] = None

    # name of company how requested this dossier
    company: Optional[str] = None

    lang: Optional[str] = None
    status: Optional[str] = None
    count_deleted_semantic_documents: Optional[int] = None
    count_deleted_semantic_pages: Optional[int] = None
    creation: datetime
    extracted_files_v2: Optional[Dict[str, ExtractedFileV2]] = None
    extracted_files: Dict[str, ExtractedFile]
    photos_sorting: Optional[bool] = False
    doccheck_case_id: Optional[UUID] = None


class SemanticDossier(SemanticDossierDefault):
    dossier_files: Optional[Dict[str, DossierFile]] = None
    original_files: Optional[Dict[str, OriginalFile]] = None
    processing_exceptions: Dict[str, FileException]
    processed_files: Dict[str, ProcessedFileFullApiData]
    semantic_documents: List[SemanticDocumentFullApiData]


class SemanticDossierSimple(SemanticDossierDefault):
    class RoleUsername(BaseModel):
        first_name: str
        last_name: str
        username: str
        role: UUID
        role_key: str

    processing_exceptions: Dict[str, FileException]
    semantic_documents: List[SemanticDocumentFullApiDataCreatedAt]
    processed_files: Dict[str, ProcessedFileSimple]
    count_original_files: Optional[int] = None
    expiry_date: Optional[datetime] = None
    max_expiry_date: Optional[datetime] = None
    businesscase_type_id: Optional[UUID] = None
    access_mode: AccessMode
    work_status_id: Optional[UUID] = None
    bekb: Any
    owner_username: str
    role: List[UUID] = []
    role_keys: List[str] = []
    role_username: List[RoleUsername] = []
    external_id: Optional[str] = None


class ExtractedFileCreate(BaseModel):
    original_file_uuid: UUID
    dossier_file_uuid: UUID
    path_from_original: str


class BoundingBox(BaseModel):
    model_config = ConfigDict(frozen=True)

    ref_width: int  # full width of the page/image
    ref_height: int  # full height of the page/image
    top: int
    left: int
    right: int
    bottom: int


class ConfidenceLevel(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CERTAIN = "certain"


class PageObject(BaseModel):
    model_config = ConfigDict(frozen=True)

    # 240909 mt: added to resolve ambiguity of several identical page objects
    # Is optional for backwards compatibilty, can be mandatory if processing always deliveres a uuid
    uuid: Optional[UUID] = None

    key: str
    title: str

    titles: PageObjectTitles

    value: Optional[str] = None
    type: str
    bbox: BoundingBox
    page_number: int

    # Flag that indicates if this page object should be presented in the frontend
    # All page objects will always be visible in the debug information
    visible: Optional[bool] = True

    # Internal explanation why object is invisible (debugging only)
    invisibility_reason: Optional[str] = None

    # confidence: float

    confidence_value: float
    confidence_formatted: str
    confidence_level: ConfidenceLevel


class DocumentCategory(BaseModel):
    id: str
    name: str
    de: Optional[str] = None
    en: Optional[str] = None
    fr: Optional[str] = None
    it: Optional[str] = None
    description_de: Optional[str] = None
    description_en: Optional[str] = None
    description_fr: Optional[str] = None
    description_it: Optional[str] = None
    de_external: Optional[str] = None
    en_external: Optional[str] = None
    fr_external: Optional[str] = None
    it_external: Optional[str] = None
    additional_search_terms_de: Optional[str] = None
    additional_search_terms_en: Optional[str] = None
    additional_search_terms_fr: Optional[str] = None
    additional_search_terms_it: Optional[str] = None
    exclude_for_recommendation: Optional[bool] = None


class DocumentCategoryTranslated(BaseModel):
    # name as natural key
    name: str

    # id of the document category
    id: str

    # translation of the document category according to the dossier language
    translation: str

    de_external: Optional[str] = None
    en_external: Optional[str] = None
    fr_external: Optional[str] = None
    it_external: Optional[str] = None
    additional_search_terms_de: Optional[str] = None
    additional_search_terms_en: Optional[str] = None
    additional_search_terms_fr: Optional[str] = None
    additional_search_terms_it: Optional[str] = None


class PageCategory(BaseModel):
    id: int
    name: str


class ProcessedPage(BaseModel):
    uuid: UUID
    number: int
    lang: str

    document_category: DocumentCategory
    page_category: PageCategory
    # confidence: float

    page_objects: List[PageObject]

    image_dossier_file_uuid: Optional[UUID] = None
    searchable_pdf_dossier_file_uuid: Optional[UUID] = None
    searchable_txt_dossier_file_uuid: Optional[UUID] = None

    image_file_url: Optional[str] = None
    searchable_pdf_file_url: Optional[str] = None
    searchable_txt_file_url: Optional[str] = None


class ProcessedFile(BaseModel):
    extracted_file_uuid: UUID

    pages: Dict[int, ProcessedPage]


class SemanticPage(BaseModel):
    processed_page_uuid: UUID
    lang: str

    number: int

    page_category: (
        PageCategory  # assigned in step create_semantic_document / create_semantic_page
    )
    document_category: DocumentCategory

    confidence_value: float
    confidence_formatted: str
    confidence_level: ConfidenceLevel

    page_objects: List[PageObject]


class SemanticDocument(BaseModel):
    title: Optional[str] = None
    title_suffix: Optional[str] = None

    confidence_value: float
    confidence_formatted: str
    confidence_level: ConfidenceLevel

    semantic_pages: List[SemanticPage]

    # doc cat including a potential company mapping
    document_category: DocumentCategory

    # doc cat without mapping according to master document list
    document_category_original: DocumentCategory

    aggregated_objects: List[PageObject]


class DocumentProcessingResult(BaseModel):
    dossier_uuid: Optional[UUID] = None
    processed_file: ProcessedFile
    semantic_documents: List[SemanticDocument]


class SaveResultDND(BaseModel):
    saved: bool


class RequestBodyResultDND(BaseModel):
    semantic_document_uuid: UUID
    data: Optional[SemanticDocumentFullApiData] = None

    # This was annoying to figure out
    # The following keys were removed: `json_encoders`.
    # Check https://docs.pydantic.dev/dev-v2/migration/#changes-to-config for more information.
    model_config = ConfigDict(json_encoders={UUID: lambda v: str(v)})


class RequestBodyResultDNDList(RootModel):
    root: List[RequestBodyResultDND]


class SemanticDossierName(BaseModel):
    name: str
    expiry_date: Optional[datetime] = None
    owner_username: Optional[str] = None
    businesscase_type_id: Optional[str] = None
    work_status_id: Optional[UUID] = None
    dossier_role: Optional[str] = None


class StatusUpdate(BaseModel):
    need_update: bool


class FilesToCompare(BaseModel):
    uuid: str
    last_update: str


# Dummy UUID that will never be sent from the frontend
BCT_NOT_INITIALIZED = str(UUID("94d2a2e6-39d2-42e5-aee3-ff450f998bad"))


class CheckDataToCompare(BaseModel):
    original_files: List[FilesToCompare]
    extracted_files: List[FilesToCompare]
    exceptions: List[FilesToCompare]

    note: Optional[str] = None
    name: Optional[str] = None

    # 240411 mt: needed if the businesscase type is updated by doccheck
    # This will trigger an update of the businesscase type in the dossier with django signals
    businesscase_type_id: Optional[str] = BCT_NOT_INITIALIZED


class DownloadSchema(BaseModel):
    url: str
    filename: str


class OpenSchema(BaseModel):
    url: str


class EditPageObjectSchema(BaseModel):
    page_object_value: Optional[str] = None
    bbox: Optional[BoundingBox] = None


class DossierDataToCompare(BaseModel):
    dossier_uuid: str
    updated_at: str
    status: str


class DossierNoteRequestBody(BaseModel):
    dossier_note: str


class DossierNoteResponse(BaseModel):
    dossier_uuid: UUID
    dossier_note: str


class DossierBekbBusinessCaseResponse(BaseModel):
    dossier_uuid: UUID
    business_number: Optional[str] = None


class UpdatePageObjectConfidenceRequest(BaseModel):
    confidence_value: int


class PageBasics(BaseModel):
    number: int
    searchable_pdf_url: str
    image_url: str
    fre_xml_url: Optional[str] = None


class FileBasics(BaseModel):
    """Data Model for export into classifier"""

    uuid: str = uuid4()
    source: str
    file_url: str
    pages: List[PageBasics]


class Message(BaseModel):
    detail: str


class DocumentCategoryLoad(BaseModel):
    name: str
    id: str
    de: Optional[str] = None
    en: Optional[str] = None
    fr: Optional[str] = None
    it: Optional[str] = None
    description_de: Optional[str] = None
    description_en: Optional[str] = None
    description_fr: Optional[str] = None
    description_it: Optional[str] = None
    additional_search_terms_de: Optional[str] = None
    additional_search_terms_en: Optional[str] = None
    additional_search_terms_fr: Optional[str] = None
    additional_search_terms_it: Optional[str] = None
    exclude_for_recommendation: bool
    de_external: Optional[str] = None
    en_external: Optional[str] = None
    fr_external: Optional[str] = None
    it_external: Optional[str] = None


class DossierWorkStatusChangedEvent(EventDetail):
    dossier_uuid: UUID
    username: str
    from_state_key: Optional[str] = None
    to_state_key: Optional[str] = None


class AccessDelegationNew(BaseModel):
    delegator_username: str
    delegate_username: str
    expiry_date: Optional[datetime] = None


class AccessDelegationAccount(BaseModel):
    username: str
    firstname: Optional[str] = None
    lastname: Optional[str] = None


class Delegation(BaseModel):
    uuid: UUID
    user: AccessDelegationAccount
    expiry_time: Optional[datetime] = None


class AccessDelegation(BaseModel):
    list_of_delegate: List[Delegation]
    list_of_delegator: List[Delegation]


class AccessDelegationPermission(BaseModel):
    account_have_access_delegation_permission: bool


class AccessDelegationAccounts(BaseModel):
    list_of_users_on_same_account: List[AccessDelegationAccount]


class Language(str, Enum):
    de = "de"
    fr = "fr"
    it = "it"
    en = "en"


class RealEstateProperty(BaseModel):
    key: Annotated[str, StringConstraints(max_length=255)]
    title: Optional[Annotated[str, StringConstraints(max_length=255)]] = None
    floor: Optional[int] = None
    street: Optional[Annotated[str, StringConstraints(max_length=255)]] = None
    street_nr: Optional[Annotated[str, StringConstraints(max_length=10)]] = None
    zipcode: Optional[Annotated[str, StringConstraints(max_length=60)]] = None
    city: Optional[Annotated[str, StringConstraints(max_length=255)]] = None


class EntityTypes(str, Enum):
    REALESTATEPROPERTY = "realestateproperty"
    NONE = ""


class DateRange(BaseModel):
    from_date: Optional[str] = None
    to_date: Optional[str] = None


class DossierAccessGrant(BaseModel):
    dossier_uuid: UUID
    requested: datetime
    issued: datetime
    expires: datetime
    has_access: bool


class StatusSchema(BaseModel):
    uuid: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    state_machine_uuid: Optional[str] = None  # Reference to parent state machine
    key: str
    name_de: Optional[str] = None
    description_de: Optional[str] = None
    name_fr: Optional[str] = None
    description_fr: Optional[str] = None
    name_it: Optional[str] = None
    description_it: Optional[str] = None
    name_en: Optional[str] = None
    description_en: Optional[str] = None
    color: Optional[str] = "#FF0000"
    order: Optional[int] = 0


class StateMachineSchema(BaseModel):
    uuid: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    name: str
    start_status: Optional[StatusSchema] = None


class DocCheckSchema(BaseModel):
    uuid: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    key: str
    description: Optional[str] = None


class DossierAccessCheckProviderSchema(BaseModel):
    uuid: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    name: str


class AccountKey(BaseModel):
    key: str


class FeatureFlagsMixin(BaseModel):
    valid_dossier_languages: List[str]
    valid_ui_languages: List[str]
    show_document_category_external: bool
    show_business_case_type: bool
    frontend_theme: str
    photo_album_docx_template: str
    instructions_menu_key: str
    enable_button_create: bool
    enable_feedback_form: bool
    enable_download_original_file_link: bool
    enable_show_deleted_elements: bool
    enable_dossier_sorting: bool
    enable_error_detail: bool
    enable_dossier_search: bool
    enable_zoom_feature: bool
    enable_debug_document: bool
    enable_download_dossier: bool
    enable_download_document: bool
    enable_icons_on_page_view: bool
    enable_uploading_files: bool
    enable_drag_and_drop_in_page_view: bool
    enable_rendering_structure_tab: bool
    enable_hovered_section_on_page_view: bool
    enable_rendering_hurdles_tab: bool
    enable_area_calculator: bool
    enable_semantic_document_confidence: bool
    enable_semantic_document_export: bool
    enable_bekb_export: bool
    enable_bekb_automatic_collateral: bool
    enable_button_open_in_new_tab: bool
    enable_rendering_photos_tab: bool
    enable_rendering_structure_details_tab: bool
    enable_rendering_bekb_mortgage_archiving_tab: bool
    enable_button_dossier_settings: bool
    enable_button_dossier_notes: bool
    enable_button_download: bool
    enable_document_upload: bool
    enable_documents_delta_view: bool
    enable_semantic_page_image_lazy_loading: bool
    enable_rendering_plans_tab: bool
    enable_real_estate_properties: bool
    enable_dossier_assignment: bool
    enable_dossier_assignment_to_someone_else: bool
    enable_form_tab: bool
    enable_custom_semantic_document_date: bool
    enable_semantic_document_annotations: bool
    enable_semantic_document_splitting: bool
    enable_automatic_semantic_document_splitting: bool
    enable_download_extraction_excel: bool
    enable_dossier_permission: bool
    enable_download_metadata_json: bool
    document_download_ui_add_uuid_suffix: bool


# Create an optional version of FeatureFlagsMixin
OptionalFeatureFlagsMixin = create_model(
    "OptionalFeatureFlagsMixin",
    **{
        name: (Optional[field.annotation], None)
        for name, field in FeatureFlagsMixin.model_fields.items()
    },
)


class AccountChangeSchema(OptionalFeatureFlagsMixin):
    key: Optional[str] = None
    dmf_endpoint: Optional[str] = None
    name: Optional[str] = None
    default_bucket_name: Optional[str] = None
    default_dossier_expiry_duration_days: Optional[int] = None
    max_dossier_expiry_duration_days: Optional[int] = None
    document_export_strategy: Optional[ExportStrategy] = None
    navigation_strategy: Optional[NavigationStrategy] = None
    state_machine: Optional[UUID] = None
    active_work_status_state_machine: Optional[UUID] = None
    active_doc_check: Optional[UUID] = None
    allow_dossier_listing: Optional[bool] = None
    dossier_access_check_provider: Optional[UUID] = None
    dossier_access_check_error_component: Optional[DossierAccessCheckErrorComponent] = (
        None
    )

    @field_validator("active_work_status_state_machine", mode="before")
    @classmethod
    def get_active_work_status_state_machine(cls, v: object) -> object:
        if isinstance(v, StateMachine):
            return v.uuid
        return v

    @field_validator("active_doc_check", mode="before")
    @classmethod
    def get_active_doc_check(cls, v: object) -> object:
        if isinstance(v, DocCheck):
            return v.uuid
        return v

    @field_validator("dossier_access_check_provider", mode="before")
    @classmethod
    def get_dossier_access_check_provider(cls, v: object) -> object:
        if isinstance(v, DossierAccessCheckProvider):
            return v.uuid
        return v

    model_config = ConfigDict(str_strip_whitespace=True)


class AccountCreateSchema(AccountChangeSchema):
    key: str
    name: str
    default_bucket_name: str


class AccountResponseSchema(AccountChangeSchema):
    uuid: UUID
    created_at: datetime
    updated_at: datetime
    model_config = ConfigDict(str_strip_whitespace=True, from_attributes=True)


class DossierAccountResponse(FeatureFlagsMixin):
    account_name: str
    default_language: str
    all_languages: List[str]
    navigation_strategy: Optional[NavigationStrategy] = None
    is_bekb: bool
    state_machine: Optional[UUID] = None
    active_doc_check: Optional[UUID] = None
    dossier_access_check_error_component: DossierAccessCheckErrorComponent


class AccountUser(BaseModel):
    # Note: Do not use user.email for username
    username: str
    firstname: Optional[str] = None
    lastname: Optional[str] = None
    # Account user uuid, not to be confused with user id
    account_user_uuid: UUID


class DossierAvailableRolesSchema(BaseModel):
    available_dossier_roles: List[DossierRole]


class FilterUser(BaseModel):
    # Note: Do not use user.email for username
    username: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None


class DossierRoleFilterUsersSchema(BaseModel):
    filter_users: List[FilterUser]


class DossierAssignableUsersSchema(BaseModel):
    assignable_users_list: List[AccountUser]


class SemanticDocumentBasics(BaseModel):
    uuid: UUID
    document_category__name: Optional[str] = None
    confidence_level: Optional[str] = None
    confidence_formatted: Optional[str] = None
    confidence_value: Optional[float] = None
    title_custom: Optional[str] = None
    title_suffix: Optional[str] = None
    last_page_change_date: Optional[datetime] = None
    last_entity_change_date: Optional[datetime] = None
    access_mode: Optional[AccessMode] = None
    external_semantic_document_id: Optional[str] = None


class SemanticDocumentBasicsList(BaseModel):
    dossier_uuid: UUID
    semantic_documents: List[SemanticDocumentBasics]


class JWKSchema(BaseModel):
    class AccountUUIDKEYSchema(BaseModel):
        uuid: UUID
        key: str
        model_config = ConfigDict(str_strip_whitespace=True, from_attributes=True)

    account: AccountUUIDKEYSchema

    jwk: KeyPair
    enabled: bool
    description: Optional[str] = None
    model_config = ConfigDict(str_strip_whitespace=True, from_attributes=True)


class JWKChangeSchema(BaseModel):
    class AccountUUIDSchema(BaseModel):
        uuid: UUID

    account: AccountUUIDSchema

    jwk: Optional[KeyPair] = None
    enabled: Optional[bool] = True
    description: Optional[str] = None
    model_config = ConfigDict(str_strip_whitespace=True, from_attributes=True)


class JWKSetSchema(BaseModel):
    class AccountUUIDSchema(BaseModel):
        uuid: UUID

    account: AccountUUIDSchema

    jwk: KeyPair
    enabled: Optional[bool] = None
    description: Optional[str] = None
    model_config = ConfigDict(str_strip_whitespace=True, from_attributes=True)


class DossierCopyRequest(BaseModel):
    # Copy a dossier
    source_dossier_uuid: UUID
    new_external_dossier_id: str


class DossierCopyContentsIntoExistingDossierRequest(BaseModel):
    # Copy the contents of a dossier into another (empty) dossier
    source_dossier_uuid: UUID
    target_dossier_uuid: UUID
    include_deleted: Optional[bool] = True
    access_mode: Optional[AccessMode] = None
