"""
Common test utilities for the dossier application.
"""

import structlog
from workers import schemas as worker_schemas
from workers.workers import process_semantic_dossier_pdf_request
from dossier.management.commands.dossier_event_consumer_v2 import (
    set_semantic_document_export_done,
)

logger = structlog.get_logger(__name__)


def mock_publish_side_effect_services_rabbit_mq_publish(*args, **kwargs):
    """
    Mock function for the RabbitMQ publish service used in tests.

    This function simulates the behavior of the RabbitMQ publish service by:
    1. Processing the PDF generation request
    2. Setting the semantic document export as done

    Args:
        *args: Variable length argument list
        **kwargs: Arbitrary keyword arguments, should contain 'message'

    Returns:
        None
    """
    request = worker_schemas.SemanticDocumentPDFRequestV1.model_validate_json(
        kwargs["message"]
    )

    # Process the pdf generation
    # Returns json dump in format SemanticDocumentPDFResponseV1
    process_semantic_document_response = process_semantic_dossier_pdf_request(
        semantic_document_pdf_request=request
    )

    # which is then collected by dossier events consumer
    # and sets event as done
    set_semantic_document_export_done(process_semantic_document_response)
