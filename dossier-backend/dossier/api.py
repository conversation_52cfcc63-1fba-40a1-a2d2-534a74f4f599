from datetime import timedelta, datetime
from functools import cmp_to_key
from http import HTTPStatus
from operator import attrgetter
from typing import List, Any, Optional, Union
from uuid import UUID, uuid4

import structlog
from django.apps import apps
from django.contrib.auth.models import User
from django.core.paginator import Paginator
from django.db.models import Case, Count, F, Q, QuerySet, Value, When
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.utils.text import get_valid_filename
from ninja import File, Router
from ninja.errors import HttpError
from ninja.files import UploadedFile
from ninja.responses import codes_4xx

import core
import statemgmt.schemas as smgmt_schema
from bekb.models import BEKBDossierProperties, BusinessCase
from bekb.models import Partner
from core.schema import Message
from dossier import helpers as dossier_helpers, services, jwt_extract
from dossier import schemas
from dossier.doc_cat_helpers import (
    load_document_categories,
)
from dossier.helpers_access_check import (
    get_dossier_from_request_with_access_check,
    get_dossier_with_access_check,
    get_dossier_from_request_with_access_check_async,
    get_writable_dossier_from_request_with_access_check,
)
from dossier.helpers_model_copier import copy_dossier_models
from dossier.helpers_v2 import (
    get_bekb_dossier_properties,
    get_dossier_roles,
    check_enable_dossier_permission,
    generate_list_users_on_same_account,
    generate_list_delegate,
    generate_list_delegator,
    get_semantic_pages,
    serialize_semantic_page,
    sort_semantic_page,
)
from dossier.helpers_v2 import (
    prepare_semantic_dossier_v2,
    prepare_semantic_documents_status,
)
from dossier.models import (
    Dossier,
    OriginalFile,
    DocumentCategory,
    DossierFile,
    FileStatus,
    Languages,
    available_document_categories,
    Account,
    BusinessCaseType,
    DossierRole,
    UserInvolvement,
    DossierUser,
    annotate_with_calculated_access_mode,
    AccessDelegation,
    ProcessingStrategy,
    OriginalFileSource,
)
from dossier.page_objects import get_page_object_from_api, update_page_object
from dossier.processing_config import (
    OriginalFileProcessingConfig,
    SemanticDocumentSplittingStyle,
)
from dossier.schemas import (
    AccountKey,
    DossierAccountResponse,
    AccountUser,
    DossierAssignableUsersSchema,
    DossierAvailableRolesSchema,
    DossierRoleFilterUsersSchema,
    FilterUser,
)
from dossier.services import (
    dossier_list,
    get_list_of_assigned_dossiers,
    get_users_on_same_account,
    check_delegation_exist,
    update_delegation_expire_time,
    delegate_user_exists,
    delegation_list,
    get_or_create_case_to_dossier_if_active_doccheck,
    map_splitting_style_pc_to_of,
    is_pers,
    get_aggregate_page_objects_for_dossier,
    get_grant_token_for_dossier,
)
from dossier.services_external import create_dossier_state_context
from dossier.tasks import process_original_file
from dossier_zipper.dossier_zipper_api import generate_offline_dossier
from semantic_document.schemas_page_annotations import (
    AnnotationType,
    UserAnnotationsSchema,
)
from semantic_document.services_pdf import (
    create_semantic_document_pdf,
)
from image_exporter import schemas as dossier_zipper_schemas
from image_exporter.helpers import generate_images
from projectconfig.authentication import (
    JWTAuth,
    Auth,
    JWTAuthRequest,
    async_auth,
)
from projectconfig.settings import ENABLE_PAGE_OBJECTS_IN_DATA_V2
from semantic_document import helpers
from semantic_document.models import (
    SemanticDocument,
    SemanticPagePageObject,
    SemanticPageUserAnnotations,
    SemanticPage,
)
from semantic_document.schemas import SavingResultWithMessage
from semantic_document.services import (
    create_semantic_document_pdf_filename,
    assert_semantic_document_is_writable,
)
from statemgmt.models import Status, StateTransition
from statemgmt.services import calculate_next_possible_states

dossier_router = Router()
core_router = Router()
access_delegation_router = Router()

logger = structlog.getLogger(__name__)


@dossier_router.get("/check-access", response={200: schemas.DossierListAccessDecision})
def check_dossier_access_list(request):
    jwt = request.jwt
    account = jwt_extract.get_account(jwt)
    if account.allow_dossier_listing or jwt_extract.is_manager(jwt):
        return schemas.DossierListAccessDecision(access=True)
    return schemas.DossierListAccessDecision(access=False)


@dossier_router.get("/", response=schemas.PaginatedDossier)
def list_dossier(
    request: JWTAuthRequest,
    number_page: int,
    page_size: int = 50,
    type_ordering: str = "desc",
    search_value: str = None,
    ordering_value: str = "created_at",
    filter_businesscase_type: str = None,
    filter_dossier_role: str = None,
    filter_status_type: str = None,
    filter_business_partner_type: str = None,
    filter_owner_username: str = None,
):
    """
    Lists all dossier the user has access to

    """

    dossier_user = request.auth

    # This is a comma separated list of usernames that are filtered (in any of the user filters, so ASSIGNEE or FICO)
    # Needs refactoring to split the usernames per dossier_role
    filter_user_involvment_usernames = filter_owner_username

    is_a_pers_user = is_pers(dossier_user)

    is_manager = request.is_manager
    username = request.auth.user.username
    account_key = request.auth.account.key

    if not request.auth.account.allow_dossier_listing and not is_manager:
        raise HttpError(
            401,
            "Not allowed to list dossiers (allow_dossier_listing and not dossier manager)",
        )

    query_set = dossier_list(
        username,
        account_key,
        filter_businesscase_type,
        filter_dossier_role,
        filter_user_involvment_usernames,
        filter_status_type,
        is_a_pers_user,
        ordering_value,
        filter_business_partner_type,
        search_value,
        type_ordering,
        is_manager,
    )

    enable_dossier_permission = request.auth.account.enable_dossier_permission
    filter_owner_username_list = (
        filter_owner_username.split(",") if filter_owner_username else []
    )

    assigned_dossiers = get_list_of_assigned_dossiers(
        username,
        account_key,
        filter_owner_username_list,
        search_value,
        enable_dossier_permission,
    )

    query_set = query_set | assigned_dossiers

    query_set = annotate_with_calculated_access_mode(query_set, request.auth.user)

    paginator = Paginator(
        query_set,
        page_size,
    )
    page_obj = paginator.get_page(number_page)

    list_objects = page_obj.object_list

    list_dossiers = [
        schemas.Dossier(
            **{
                **d.__dict__,
                "access_mode": d.calculated_access_mode,
                "bekb_dossier_properties": get_bekb_dossier_properties(d),
                "dossier_roles": get_dossier_roles(d),
            }
        )
        for d in list_objects
    ]

    return schemas.PaginatedDossier(
        count=paginator.num_pages,
        total_dossiers_count=paginator.count,
        data=list(list_dossiers),
    )


@access_delegation_router.get(
    "/have_permission", response={200: schemas.AccessDelegationPermission}
)
def get_account_have_access_delegation_permission(request):
    # check if the account has permission to access this
    have_permission = check_enable_dossier_permission(request.auth.account)
    return schemas.AccessDelegationPermission(
        account_have_access_delegation_permission=have_permission
    )


@access_delegation_router.get(
    "/", response={200: schemas.AccessDelegation, 403: core.schema.Message}
)
def get_list_access_delegation(request: JWTAuthRequest):
    user = request.auth.user

    account = request.auth.account

    # check if the account have permission to access this feature
    if not check_enable_dossier_permission(account):
        return 403, {"detail": "Account not allowed to access this feature"}

    delegate = delegation_list("delegate", user, account=account)
    delegator = delegation_list("delegator", user, account=account)

    # list of delegate
    list_of_delegate = generate_list_delegate(delegate)

    # list of delegator
    list_of_delegator = generate_list_delegator(delegator)

    result = schemas.AccessDelegation(
        list_of_delegate=list_of_delegate, list_of_delegator=list_of_delegator
    )

    return result


@access_delegation_router.get(
    "/delegation_accounts/{search}",
    response={200: schemas.AccessDelegationAccounts, 403: core.schema.Message},
)
def get_list_access_delegation_accounts(request: JWTAuthRequest, search: str):
    username = request.auth.user.username
    account = request.auth.account

    # check if the account have permission to access this feature
    if not check_enable_dossier_permission(account):
        return 403, {"detail": "Account not allowed to access this feature"}

    users_on_same_account = get_users_on_same_account(account, username, search)

    list_of_users_on_same_account = generate_list_users_on_same_account(
        users_on_same_account
    )

    return schemas.AccessDelegationAccounts(
        list_of_users_on_same_account=list_of_users_on_same_account
    )


@access_delegation_router.post(
    "/",
    response={
        200: schemas.CreatedObjectReference,
        400: core.schema.Message,
        403: core.schema.Message,
    },
)
def add_access_delegation(
    request, access_delegation_schema: schemas.AccessDelegationNew
):
    account = request.auth.account

    # check if the account have permission to access this feature
    if not check_enable_dossier_permission(account):
        return 403, {"detail": "Account not allowed to access this feature"}

    delegate_user = get_object_or_404(
        User, username=access_delegation_schema.delegate_username
    )
    delegator_user = request.auth.user
    expire_time = access_delegation_schema.expiry_date

    if (
        access_delegation_schema.delegate_username
        == access_delegation_schema.delegator_username
    ):
        return 400, {"detail": "can not assign  delegation to yourself"}

    # check the account for delegate_user and delegator_user exist
    delegate_user_account_not_found = delegate_user_exists(delegate_user, account)
    delegator_user_account_not_found = delegate_user_exists(delegator_user, account)

    account_msg = (
        "Both "
        if (delegate_user_account_not_found and delegator_user_account_not_found)
        else (
            "Delegate"
            if delegate_user_account_not_found
            else "Delegator" if delegator_user_account_not_found else "None"
        )
    )

    add_s = (
        "'s"
        if (delegate_user_account_not_found and delegator_user_account_not_found)
        else ""
    )

    if delegate_user_account_not_found or delegator_user_account_not_found:
        return 400, {"detail": f"{account_msg} Account{add_s} not found"}

    # check for record existence
    access_delegation_exist = check_delegation_exist(
        account, delegator_user, delegate_user
    )

    if access_delegation_exist:
        access_delegation = update_delegation_expire_time(
            account, delegator_user, delegate_user, expire_time
        )
        return access_delegation

    access_delegation = AccessDelegation.objects.create(
        account=account,
        delegator=delegator_user,
        delegate=delegate_user,
        expire_time=expire_time,
    )

    return access_delegation


@access_delegation_router.delete(
    "/{access_delegation_uuid}", response={204: None, 403: core.schema.Message}
)
def delete_access_delegation(request: JWTAuthRequest, access_delegation_uuid: UUID):
    # check if the account have permission to access this feature
    if not check_enable_dossier_permission(request.auth.account):
        return 403, {"detail": "Account not allowed to access this feature"}

    access_delegation_ = get_object_or_404(
        AccessDelegation, uuid=access_delegation_uuid
    )
    access_delegation_.delete()
    return 204, ""


@dossier_router.post("/check_update", response=schemas.StatusUpdate)
def check_dossiers(
    request: JWTAuthRequest, dossier_data: List[schemas.DossierDataToCompare]
):
    status = False
    list_uuid = list(
        map(lambda request_dossier: request_dossier.dossier_uuid, dossier_data)
    )

    # 230509 mt:
    # If access delegation is enabled we do not check for owner because
    # list of dossiers is anyway filtered by list of uuid and we do not return
    # sensitive data
    # If access delegation is disabled normal checking for owner happens
    user_checked = (
        None if request.auth.account.enable_dossier_permission else request.auth.user
    )

    filter_kwargs = dossier_helpers.get_manager_filter(
        request.is_manager, user_checked, request.auth.account
    )

    dossier_list = list(
        Dossier.objects.filter(uuid__in=list_uuid, **filter_kwargs)
        .annotate(
            status=Count(
                "original_files", filter=Q(original_files__status=FileStatus.PROCESSING)
            )
        )
        .annotate(
            status=Case(
                When(status=0, then=Value(FileStatus.PROCESSED)),
                When(status__gt=0, then=Value(FileStatus.PROCESSING)),
            )
        )
        .values("uuid", "updated_at", "status")
    )

    if len(dossier_list) != len(dossier_data):
        status = True

    if not status:
        for dossier_request in dossier_data:
            dossier = dossier_helpers.find_uuid_in_dossier_list(
                dossier_list, dossier_request.dossier_uuid
            )
            if dossier is None or dossier_helpers.compare_dossier_manager_data(
                dossier, dossier_request
            ):
                status = True
                break

    return {"need_update": status}


@dossier_router.get("/{dossier_uuid}", response=schemas.Dossier, url_name="get-dossier")
def get_dossier(request: JWTAuthRequest, dossier_uuid: UUID):
    """get dossier by dossier_uuid"""
    dossier_qs: QuerySet[Dossier] = Dossier.objects
    dossier_qs = annotate_with_calculated_access_mode(dossier_qs, request.auth.user)
    dossier = get_dossier_from_request_with_access_check(
        request, dossier_uuid, dossier_qs
    )
    # add a case to the dossier if the associated account has an active doccheck and no case is present yet
    get_or_create_case_to_dossier_if_active_doccheck(dossier)

    dossier_data = dossier_helpers.prepare_for_api_dossier_data(dossier)

    # Todo: remove dossier roles, or set to = [], once frontend starts to use get api:dossier-properties
    # for getting current dossier assignment
    # We set dossier_data here, otherwise we get a circular import via helpers and helpers_v2
    dossier_data["dossier_roles"] = get_dossier_roles(dossier=dossier)
    # Also consider removing owner_username once we move to get api:dossier-properties
    # We are setting these here, otherwise we break prepare_semantic_dossier in helpers v2
    # Rather than writing helpers v2 cleanly, prepare_semantic_dossier depends on prepare_for_api_dossier_data
    # in helpers v1
    dossier_data["owner_username"] = dossier.owner.username
    dossier_data["owner_first_name"] = dossier.owner.first_name
    dossier_data["owner_last_name"] = dossier.owner.last_name

    return dossier_data


@dossier_router.get(
    "/external_id/{external_dossier_id}",
    response={200: schemas.Dossier},
    url_name="get_dossier_external_id",
)
def get_dossier_external_id(request: JWTAuthRequest, external_dossier_id: str):
    """get dossier by external id"""
    dossier_user: DossierUser = request.auth
    dossier_qs = annotate_with_calculated_access_mode(
        Dossier.objects, dossier_user.user
    )

    signed_external_dossier_id = jwt_extract.get_external_dossier_id(request.jwt)
    use_external_dossier_id = (
        signed_external_dossier_id
        if signed_external_dossier_id
        else external_dossier_id
    )

    dossier_qs = dossier_qs.filter(
        external_id=use_external_dossier_id, account__key=dossier_user.account.key
    )
    dossier = get_dossier_with_access_check(
        dossier_user=request.auth,
        is_manager=request.is_manager,
        dossier_qs=dossier_qs,
        external_dossier_id=use_external_dossier_id,
    )

    return dossier_helpers.prepare_for_api_dossier_data(dossier)


@dossier_router.patch("/{dossier_uuid}", response=SavingResultWithMessage)
def delete_dossier(request: JWTAuthRequest, dossier_uuid: UUID):
    """Update dossier field expiry_date"""
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)
    username = request.auth.user.username
    old_expiry_date = dossier.expiry_date
    dossier.expiry_date = timezone.now() - timedelta(days=1)
    new_expiry_date = dossier.expiry_date
    dossier.save()

    logger.info(
        "DOSSIER_CHANGE: dossier has been soft-deleted",
        dossier_uuid=dossier.uuid,
        old_expiry_date=old_expiry_date,
        new_expiry_date=new_expiry_date,
        owner=dossier.owner,
        name=dossier.name,
        username=username,
    )

    return {"message": "Expiry date updated"}


@dossier_router.get(
    "/{dossier_uuid}/data_v2",
    response=schemas.SemanticDossierSimple,
    url_name="get-dossier-data-v2",
)
def get_full_dossier(
    request: JWTAuthRequest, dossier_uuid: UUID, show_soft_deleted: bool = False
):
    dossier_qs = dossier_helpers.get_dossier_queryset()
    # Add the calculated_access_mode to the queryset
    dossier_qs = annotate_with_calculated_access_mode(dossier_qs, request.auth.user)
    dossier = get_dossier_from_request_with_access_check(
        request, dossier_uuid, dossier_qs
    )

    return create_semantic_dossier_simple(
        dossier, show_soft_deleted, enable_page_objects=ENABLE_PAGE_OBJECTS_IN_DATA_V2
    )


def create_semantic_dossier_simple(
    dossier: Dossier, show_soft_deleted, enable_page_objects=True
):
    account = dossier.account

    semantic_documents = dossier.semantic_documents.all()

    bekb_semantic_documents_status = prepare_semantic_documents_status(
        dossier, semantic_documents
    )

    data = prepare_semantic_dossier_v2(
        dossier=dossier,
        show_soft_deleted=show_soft_deleted,
        show_count_deleted_objects=True,
        enable_bekb_export=account.enable_bekb_export,
        enable_page_objects=enable_page_objects,
        include_annotations=False,
    )
    data = {
        **data,
        "bekb": (
            {
                **data["bekb"],
                "semantic_documents_statuses": bekb_semantic_documents_status,
            }
            if data["bekb"]
            else None
        ),
    }
    user_involvement = UserInvolvement.objects.filter(dossier=dossier)

    return schemas.SemanticDossierSimple(
        **data["dossier_data"],
        extracted_files_v2=data["extracted_files_v2"],
        extracted_files=data["extracted_files"],
        processing_exceptions=data["processing_exceptions"],
        processed_files=data["processed_files"],
        semantic_documents=data["semantic_documents"],
        bekb=data["bekb"],
        owner_username=dossier.owner.username,
        role=[i.role.uuid for i in user_involvement],
        role_keys=[i.role.key for i in user_involvement],
        role_username=[
            {
                "first_name": i.user.user.first_name,
                "last_name": i.user.user.last_name,
                "username": i.user.user.username,
                "role": i.role.uuid,
                "role_key": i.role.key,
            }
            for i in user_involvement
        ],
        external_id=dossier.external_id,
    )


@dossier_router.get(
    "/{dossier_uuid}/export",
    response={HTTPStatus.SEE_OTHER: schemas.Redirect},
    url_name="dossier-export",
    auth=async_auth,
)
async def export_dossier(request: JWTAuthRequest, dossier_uuid: UUID):

    # Perform access check
    dossier: Dossier = await get_dossier_from_request_with_access_check_async(
        request, dossier_uuid
    )

    account: Account = dossier.account

    dossier_export = await generate_offline_dossier(
        dossier_uuid=dossier_uuid,
        add_uuid_suffix=account.document_download_ui_add_uuid_suffix,
        add_metadata_json=account.enable_download_metadata_json,
        date_range=None,
    )

    return HTTPStatus.SEE_OTHER, schemas.Redirect(location=dossier_export.file.fast_url)


@dossier_router.patch(
    "/{dossier_uuid}/change_dossier_properties",
    response=schemas.SemanticDossierName,
    url_name="change-dossier-properties",
    deprecated=True,
)
def change_dossier_properties(
    request, dossier_uuid: UUID, dossier_schema: schemas.SemanticDossierName
):
    # Depreciated, use change_dossier_properties_v2 instead
    # this is due to update_dossier_properties handling assign user logic in a weird way

    dossier = get_writable_dossier_from_request_with_access_check(request, dossier_uuid)

    user = User.objects.get(username=dossier_schema.owner_username)
    request_user = request.auth.user

    dossier_helpers.update_dossier_properties(
        dossier, user, dossier_schema, request_user
    )

    return {
        "name": dossier.name,
        "expiry_date": dossier.expiry_date,
        "owner_username": dossier_schema.owner_username,
        "businesscase_type_id": dossier_schema.businesscase_type_id,
        "work_status_id": dossier_schema.work_status_id,
    }


@dossier_router.patch("/{dossier_uuid}/change_dossier_work_status")
def change_dossier_work_status(
    request: JWTAuthRequest,
    dossier_uuid: UUID,
    dossier_schema: schemas.ChangeDossierWorkStatus,
):
    """
    Patch endpoint to change the work status of a specified dossier.

    This function allows changing the work status of a dossier provided the transition
    is valid. It verifies the user has access to the dossier, ensures compatibility
    with the state machine model, validates the existence of a transition between
    current and new work statuses, and performs the transition if valid. The work
    status change is logged for auditing purposes.

    @param request: The request object containing authentication and authorization
        information, used to verify user's access to the dossier.
    @type request: JWTAuthRequest
    @param dossier_uuid: The unique identifier of the dossier whose work status
        is to be changed.
    @type dossier_uuid: UUID
    @param dossier_schema: Schema object containing new work status information
        required for the transition.
    @type dossier_schema: schemas.ChangeDossierWorkStatus

    @return: A dictionary containing the UUID of the updated work status after
        a successful transition.
    @rtype: dict
    """

    # Test that user has access to the dossier. Do NOT check for writable access as some of these dossiers
    # will be read-only. We rely on the state transition checks to verfiy. So a status of a read-only dossier can
    # still be changed
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    # Test that this is a valid transition
    old_work_status = dossier.work_status
    new_work_status = Status.objects.get(
        uuid=dossier_schema.work_status_id,
        state_machine=dossier.account.active_work_status_state_machine,
    )

    work_status_none = old_work_status is None
    work_status_ok = (
        old_work_status
        and old_work_status.state_machine == new_work_status.state_machine
    )
    state_machine_ok = (
        dossier.account.active_work_status_state_machine
        == new_work_status.state_machine
    )

    config_ok = (work_status_none or work_status_ok) and state_machine_ok

    if not config_ok:
        raise HttpError(400, "Invalid state machine for work status transition")
    elif work_status_none:
        # This is a special case as work status is missing on this. Initialize the work status to the initial state
        start_status = dossier.account.active_work_status_state_machine.start_status
        dossier.work_status = start_status
        dossier.save()
    elif old_work_status != new_work_status:
        transition = StateTransition.objects.filter(
            from_state=old_work_status,
            to_state=new_work_status,
            state_machine=new_work_status.state_machine,
        ).first()
        if not transition:
            raise HttpError(400, "Invalid work status transition")

        context = create_dossier_state_context(
            dossier=dossier, is_user=True, is_system=False
        )
        services.change_dossier_work_status(dossier, context, new_work_status)
        dossier.save()

    logger.info(
        f"change_dossier_work_status(account={request.auth.account}, user={request.auth.user}, dossier_uuid={dossier_uuid}, dossier_name={dossier.name}, dossier_owner={dossier.owner}, old_work_status={old_work_status}, new_work_status={dossier.work_status}) done."
    )

    return {"work_status_id": dossier.work_status.uuid}


@dossier_router.post(
    "/{dossier_uuid}/check_updates",
    response=schemas.StatusUpdate,
    url_name="check-updates",
)
def check_updates(
    request, dossier_uuid: UUID, data_to_compare: schemas.CheckDataToCompare
):
    # TODO: add test for this api call
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    request_data = data_to_compare.model_dump()

    status_update = dossier_helpers.check_update_by_count_files(dossier, request_data)
    if status_update:
        return {"need_update": status_update}

    status_update = dossier_helpers.check_update_by_dossier_properties(
        dossier, request_data
    )
    return {"need_update": status_update}


@dossier_router.post("/", response=schemas.CreatedObjectReference)
def create_a_new_dossier(request: JWTAuthRequest, dossier: schemas.CreateDossier):
    """Create new dossier with optional param name"""

    # Todo: check if user is allowed to create dossier (e.g. ZKB Users are not allowed to create dossiers via internal api except for dossier-managers, same is true for bekb, and swissfex)
    new_dossier = services.create_dossier(
        request.auth.account,
        dossier_name=dossier.name,
        language=dossier.lang,
        owner=request.auth.user,
        businesscase_type_id=dossier.businesscase_type_id,
        external_id=request.external_dossier_id,
    )

    dossier_user = DossierUser.objects.filter(
        account=new_dossier.account, user=new_dossier.owner
    ).first()
    services.change_assignee(dossier_user, new_dossier)
    return new_dossier


@dossier_router.post(
    "/{dossier_uuid}/copy",
    response={201: schemas.DossierCopyResponse},
    url_name="copy-dossier",
)
def copy_dossier(request, dossier_uuid: UUID, account: Optional[AccountKey]):
    """Creates a new Dossier based on the provided parameters"""

    user: DossierUser = request.auth

    new_dossier_account_key = None

    if account:
        new_dossier_account_key: str = account.key

    # If manager is false
    # You will only see docs you created yourself
    dossier = get_dossier_with_access_check(
        dossier_user=user,
        is_manager=True,  # Fix this to do proper ownership
        dossier_uuid=dossier_uuid,
    )
    if new_dossier_account_key:
        account: Account = Account.objects.get(key=new_dossier_account_key)

        new_instances = copy_dossier_models(
            dossier=dossier, new_dossier_account=account
        )
    else:
        new_instances = copy_dossier_models(dossier=dossier)

    new_dossier: Dossier = next(iter(new_instances.values()))

    return 201, {
        "dossier_uuid": new_dossier.uuid,
        "updated_at": new_dossier.updated_at,
        "created_at": new_dossier.created_at,
    }


@dossier_router.post(
    "/external_id/{external_dossier_id}/copy",
    response={201: schemas.DossierCopyResponse},
    url_name="copy-dossier-external_dossier_id",
)
def copy_dossier_external_id(
    request, external_dossier_id: str, dossier_copy: schemas.CopyDossier
):
    """Creates a new Dossier based on the provided parameters"""

    user: DossierUser = request.auth.get_user_or_create()

    # If manager is false
    # You will only see docs you created yourself
    dossier = get_dossier_with_access_check(
        dossier_user=user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )
    # Check if dossier already exists, and return a 409 conflict if it does

    if Dossier.objects.filter(
        account=user.account, external_id=dossier_copy.new_external_dossier_id
    ).exists():
        return 409, {
            "code": 409,
            "message": f"Dossier with external dossier id '{dossier_copy.new_external_dossier_id}' already exists",
        }

    new_instances = copy_dossier_models(
        dossier=dossier, external_id=dossier_copy.new_external_dossier_id
    )

    new_dossier: Dossier = next(iter(new_instances.values()))

    return 201, {
        "external_dossier_id": new_dossier.external_id,
        "dossier_uuid": new_dossier.uuid,
        "updated_at": new_dossier.updated_at,
        "created_at": new_dossier.created_at,
    }


@dossier_router.post(
    "/{dossier_uuid}/original_files", response=schemas.CreatedObjectReference
)
def add_original_file(
    request,
    dossier_uuid: UUID,
    file: UploadedFile = File(...),
    is_duplicate: bool = False,
):
    # This is being refactored slowly into a service
    # This one combined getting a dossier with an access check - should be two separate services
    # As auth is different for swissfex and zbk
    # First get the dossier, for access
    dossier = get_writable_dossier_from_request_with_access_check(request, dossier_uuid)

    file.name = get_valid_filename(file.name)

    original_files = (
        OriginalFile.objects.filter(dossier=dossier).prefetch_related("file").all()
    )

    exist_original_file_names = [
        original_file.file.name for original_file in original_files
    ]

    if is_duplicate is False and file.name in exist_original_file_names:
        raise HttpError(
            HTTPStatus.CONFLICT,
            f"The file {file.name} already exists in the dossier {dossier.uuid}",
        )

    file.name = helpers.create_copy_file_name(
        file.name, exist_original_file_names, rename=False
    )

    dossier_file = DossierFile.objects.create(
        dossier=dossier, data=file, bucket=dossier.bucket
    )
    original_file = OriginalFile.objects.create(
        dossier=dossier, file=dossier_file, source=OriginalFileSource.DMF.value
    )

    # This allows to test if the processing config is properly propagated to the processing of the original file
    # Add the string "_ProcessingConfigTestRequest_" in your filename to test
    if "_ProcessingConfigTestRequest_" in file.name:
        processing_config = OriginalFileProcessingConfig(
            force_document_category_key="DIVORCE_MISC",
            force_title_elements=["ProcessingConfigTestAnswerDivorce"],
            force_title_suffix="TiTlE_SuFfIx",
            semantic_document_splitting_style=SemanticDocumentSplittingStyle.NO_SPLITTING,
            enable_virus_scan=dossier.account.enable_virus_scan,
            max_num_pages_allowed_input=dossier.account.max_num_pages_allowed_input,
        )
        original_file.force_external_semantic_document_id = f"EXTID_{uuid4()}"
        original_file.force_access_mode = (
            OriginalFile.OriginalFileForceAccessMode.READ_ONLY
        )
        original_file.force_semantic_document_custom_attribute = (
            "custom value for custom_attribute"
        )

        # Store values for processing config in original file for potential reprocessing
        original_file.force_document_category_key = (
            processing_config.force_document_category_key
        )
        original_file.force_title_suffix = processing_config.force_title_suffix
        original_file.force_semantic_document_splitting_style = (
            map_splitting_style_pc_to_of(
                processing_config.semantic_document_splitting_style
            )
        )

        original_file.save()
    else:
        processing_config = None

    if dossier.account.processing_strategy == ProcessingStrategy.DEFAULT:
        process_original_file(original_file, processing_config=processing_config)
    else:
        logger.info(
            "Skip processing for original file from UI due to value of processing_strategy",
            dossier_uuid={dossier.uuid},
            dossier_external_id={dossier.external_id},
            processing_strategy=dossier.account.processing_strategy,
        )

    return original_file


@dossier_router.get(
    "/{dossier_uuid}/semantic-document/{semantic_document_uuid}/document-category-recommendations",
    response=List[schemas.DocumentCategoryTranslated],
)
def get_document_type_recommendation(
    request, dossier_uuid: UUID, semantic_document_uuid: UUID
):
    # TODO: add test for this api call
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    semantic_document: SemanticDocument = get_object_or_404(
        SemanticDocument, uuid=semantic_document_uuid, dossier=dossier
    )
    return [
        schemas.DocumentCategoryTranslated(
            name=document_category.name,
            id=document_category.id,
            translation=document_category.translated(dossier.lang),
        )
        for document_category in semantic_document.document_type_recommendations
        if not document_category.exclude_for_recommendation
    ]


@dossier_router.get(
    "/{dossier_uuid}/available-business-case-types",
    response=List[schemas.BusinessCaseType],
)
def get_available_business_case_types(request: JWTAuthRequest, dossier_uuid: UUID):
    # TODO: add test for this api call
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    account = request.auth.account

    dossier_lang = dossier.lang
    business_case_types = BusinessCaseType.objects.filter(account=account)

    return [
        schemas.BusinessCaseType(
            uuid=business_case_type.uuid,
            key=business_case_type.key,
            name=business_case_type.translated_name(f"name_{dossier_lang}"),
            description=business_case_type.translated_description(
                f"description_{dossier_lang}"
            ),
        )
        for business_case_type in business_case_types
    ]


@core_router.get(
    "/{language}/account-business-case-types", response=List[schemas.BusinessCaseType]
)
def get_account_business_case_types(request: JWTAuthRequest, language: str):
    account = request.auth.account
    business_case_types = BusinessCaseType.objects.filter(account=account)

    return [
        schemas.BusinessCaseType(
            uuid=business_case_type.uuid,
            key=business_case_type.key,
            name=business_case_type.translated_name(f"name_{language}"),
            description=business_case_type.translated_description(
                f"description_{language}"
            ),
        )
        for business_case_type in business_case_types
    ]


## ATTENTION: endpoint is wrongly named, need udpdate
@core_router.get("/bekb-dossier-properties", response=List[schemas.Partner])
def get_business_case_partner(request: JWTAuthRequest):
    business_partners = Partner.objects.filter(
        business__account=request.auth.account
    ).distinct()
    return [
        schemas.Partner(**business_partner.__dict__)
        for business_partner in business_partners
    ]


@core_router.get("/bekb-dossier-properties-filter", response=schemas.PaginatedDossier)
def get_account_property_types(
    request: JWTAuthRequest,
    business_partner_id: str,
    number_page: int,
    page_size: int = 50,
):
    business_partner_id_list = business_partner_id.split(",")
    property_list = BEKBDossierProperties.objects.filter(
        account=request.auth.account, business_partner_id__in=business_partner_id_list
    )
    dossier_id_list = [prop.dossier.uuid for prop in property_list]
    query_set = (
        Dossier.objects.filter(uuid__in=dossier_id_list)
        .annotate(
            count_documents=Count("original_files"),
            status=Count(
                "original_files", filter=Q(original_files__status=FileStatus.PROCESSING)
            ),
            creation=F("created_at"),
        )
        .annotate(
            status=Case(
                When(status=0, then=Value(FileStatus.PROCESSED)),
                When(status__gt=0, then=Value(FileStatus.PROCESSING)),
            )
        )
    )
    paginator = Paginator(
        query_set,
        page_size,
    )
    page_obj = paginator.get_page(number_page)

    l_po = page_obj.object_list

    return schemas.PaginatedDossier(
        total_dossiers_count=len(query_set), count=paginator.num_pages, data=list(l_po)
    )


@core_router.get("/dossier-status-filter", response=schemas.PaginatedDossier)
def get_account_status_types(
    request, number_page: int, dossier_status: str = "", page_size: int = 50
):
    without_status_filter = {"work_status_id__isnull": True}
    if dossier_status:
        status_list = dossier_status.split(",")
        _filter = {"work_status_id__in": list(filter(lambda x: x != "", status_list))}
        _filter = Q(**_filter)
        if "" in status_list:
            _filter = _filter | Q(**without_status_filter)

    else:
        _filter = Q(**without_status_filter)
    query_set = (
        Dossier.objects.filter(_filter, account_id=request.auth.account)
        .annotate(
            count_documents=Count("original_files"),
            status=Count(
                "original_files", filter=Q(original_files__status=FileStatus.PROCESSING)
            ),
            creation=F("created_at"),
        )
        .annotate(
            status=Case(
                When(status=0, then=Value(FileStatus.PROCESSED)),
                When(status__gt=0, then=Value(FileStatus.PROCESSING)),
            )
        )
    )
    paginator = Paginator(
        query_set,
        page_size,
    )
    page_obj = paginator.get_page(number_page)

    l_po = page_obj.object_list

    return schemas.PaginatedDossier(
        total_dossiers_count=len(query_set), count=paginator.num_pages, data=list(l_po)
    )


@dossier_router.get(
    "/{dossier_uuid}/available-document-categories",
    response=List[schemas.DocumentCategoryTranslated],
)
def get_available_document_types(
    request: JWTAuthRequest, dossier_uuid: UUID, semantic_document_uuid: str = None
):
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    doc_cat = None

    if semantic_document_uuid:
        semantic_document = get_object_or_404(
            SemanticDocument, uuid=semantic_document_uuid, dossier=dossier
        )
        doc_cat = semantic_document.document_category

    document_categories = available_document_categories(dossier.account, doc_cat)

    return [
        schemas.DocumentCategoryTranslated(
            name=document_category.name,
            id=document_category.id,
            translation=document_category.translated(dossier.lang),
            de_external=document_category.de_external,
            en_external=document_category.en_external,
            fr_external=document_category.fr_external,
            it_external=document_category.it_external,
            additional_search_terms_de=document_category.additional_search_terms_de,
            additional_search_terms_en=document_category.additional_search_terms_en,
            additional_search_terms_fr=document_category.additional_search_terms_fr,
            additional_search_terms_it=document_category.additional_search_terms_it,
        )
        for document_category in document_categories
    ]


@dossier_router.get(
    "/{dossier_uuid}/document-categories", response=List[schemas.DocumentCategory]
)
def get_document_categories(
    request,
    dossier_uuid: UUID,
    filter_names: Optional[str] = None,
    filter_recommendation: Union[bool, None] = None,
):
    """

    @param request:
    @param dossier_uuid:
    @param filter_names: Comma separated list of DocumentCategory.name, e.g. 'TAX_DECLARATION,SALARY_CERTIFICATE'. Result will contain only the categories included. If not set all categories are returned
    @param filter_recommendation: If none or False, do not filter anything. If True show only recommendations i.e. exclude_for_recommendation=False

    @return:
    """
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)
    qs = DocumentCategory.objects.filter(account=dossier.account)

    if filter_names:
        filter_names_list = filter_names.split(",")
        qs = qs.filter(name__in=filter_names_list)

    if filter_recommendation:
        qs = qs.filter(exclude_for_recommendation=False)

    document_categories = list(qs.order_by("id").all())

    return [
        schemas.DocumentCategory(
            name=document_category.name,
            id=document_category.id,
            de=document_category.de,
            en=document_category.en,
            fr=document_category.fr,
            it=document_category.it,
            de_external=document_category.de_external,
            en_external=document_category.en_external,
            fr_external=document_category.fr_external,
            it_external=document_category.it_external,
            additional_search_terms_de=document_category.additional_search_terms_de,
            additional_search_terms_en=document_category.additional_search_terms_en,
            additional_search_terms_fr=document_category.additional_search_terms_fr,
            additional_search_terms_it=document_category.additional_search_terms_it,
            description_de=document_category.description_de,
            description_en=document_category.description_en,
            description_fr=document_category.description_fr,
            description_it=document_category.description_it,
        )
        for document_category in document_categories
    ]


@dossier_router.get(
    "/{dossier_uuid}/semantic-document/{semantic_document_uuid}/title-suffix-recommendations",
    response=List[str],
)
def title_suffix_recommendations(
    request: JWTAuthRequest, dossier_uuid: UUID, semantic_document_uuid: UUID
):
    # TODO: add test for this api call
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    semantic_document = get_object_or_404(
        SemanticDocument, uuid=semantic_document_uuid, dossier=dossier
    )
    return semantic_document.title_suffix_recommendation


@dossier_router.get(
    "/{dossier_uuid}/file/{file_uuid}/download", response=schemas.DownloadSchema
)
def download_file(request: JWTAuthRequest, dossier_uuid: UUID, file_uuid: UUID):
    # TODO: add test for this api call
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    dossier_file = get_object_or_404(dossier.dossierfile_set, uuid=file_uuid)
    return schemas.DownloadSchema(
        url=dossier_file.download_url, filename=dossier_file.data.name
    )


@dossier_router.get(
    "/{dossier_uuid}/download/original_files", response=schemas.DownloadSchema
)
def download_original_files(request: JWTAuthRequest, dossier_uuid: UUID):
    # TODO: add test for this api call
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    dossier_file = dossier_helpers.create_zip_archive(dossier)
    return schemas.DownloadSchema(
        url=dossier_file.download_url, filename=dossier_file.data.name
    )


@dossier_router.post(
    "/{dossier_uuid}/file/{semantic_document_uuid}/open", response=schemas.OpenSchema
)
def open_file(
    request: JWTAuthRequest, dossier_uuid: UUID, semantic_document_uuid: UUID
):
    # TODO: add test for this api call
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    account: Account = request.auth.account

    semantic_document = get_object_or_404(
        SemanticDocument.objects.prefetch_related("semantic_pages"),
        uuid=semantic_document_uuid,
    )

    def get_annotations(page: SemanticPage):
        db_annotations = SemanticPageUserAnnotations.objects.filter(semantic_page=page)

        annotations = [
            UserAnnotationsSchema(
                annotation_group_uuid=ann.annotation_group_uuid,
                annotation_type=AnnotationType(ann.annotation_type),
                bbox_top=ann.bbox_top,
                bbox_left=ann.bbox_left,
                bbox_width=ann.bbox_width,
                bbox_height=ann.bbox_height,
                text=ann.text,
                hexcolor=ann.hexcolor,
            )
            for ann in db_annotations
        ]

        return annotations

    def get_pdf_url(page: SemanticPage):
        return page.processed_page.searchable_pdf.fast_url

    sorted_pages = sorted(
        semantic_document.semantic_pages.all(), key=cmp_to_key(sort_semantic_page)
    )

    writer = create_semantic_document_pdf(
        semantic_document=semantic_document,
        pages=sorted_pages,
        get_pdf_url_func=get_pdf_url,
        get_annotations_func=get_annotations,
        add_metadata_json=account.enable_download_metadata_json,
        dossier_language=dossier.lang,
    )

    #  There's some sort of weirdness with the single page download via the UI -
    #  the url that gets generated has a uuid added to its url and filename,
    #  however the downloaded file refuses to contain the extra UUID
    filename = create_semantic_document_pdf_filename(
        semantic_document.title,
        semantic_document.uuid,
        account.document_download_ui_add_uuid_suffix,
    )

    # clean_title = remove_invalid_chars(semantic_document.title, symbol_to_replace="_")
    # if account.document_download_ui_add_uuid_suffix:
    #     dossier_file_title = f"{clean_title}_{semantic_document.uuid}.pdf"
    # else:
    #     dossier_file_title = f"{clean_title}.pdf"

    dossier_file = dossier_helpers.save_pdf_file_to_dossier_file(
        writer=writer, dossier=dossier, title=filename
    )

    return schemas.OpenSchema(url=dossier_file.fast_url)


@dossier_router.patch(
    "/{dossier_uuid}/{semantic_page_uuid}/{page_object_uuid}",
    response=schemas.PageObjectFullApiData,
)
def update_page_object_value(
    request: JWTAuthRequest,
    dossier_uuid: UUID,
    semantic_page_uuid: UUID,
    page_object_uuid: UUID,
    edit_page_object_schema: schemas.EditPageObjectSchema,
):
    dossier = get_writable_dossier_from_request_with_access_check(request, dossier_uuid)
    semantic_page: SemanticPage = get_object_or_404(
        SemanticPage, uuid=semantic_page_uuid
    )
    assert_semantic_document_is_writable(semantic_page.semantic_document)

    # TODO: Add test for this

    page_object = get_page_object_from_api(
        dossier, page_object_uuid, semantic_page_uuid
    )

    update_page_object(page_object, edit_page_object_schema)

    return dossier_helpers.prepare_page_object(page_object)


@core_router.get("/account", response=DossierAccountResponse)
def get_dossier_account(request: JWTAuthRequest):
    account = request.auth.account
    default_lang = Languages.GERMAN.title()

    return {
        "account_name": account.name,
        "default_language": default_lang,
        "all_languages": [language.title() for language in Languages],
        "valid_dossier_languages": account.valid_dossier_languages,
        "valid_ui_languages": account.valid_ui_languages,
        "show_document_category_external": account.show_document_category_external,
        "show_business_case_type": account.show_business_case_type,
        "enable_button_create": account.enable_button_create,
        "enable_feedback_form": account.enable_feedback_form,
        "enable_download_original_file_link": account.enable_download_original_file_link,
        "enable_show_deleted_elements": account.enable_show_deleted_elements,
        "enable_dossier_sorting": account.enable_dossier_sorting,
        "enable_error_detail": account.enable_error_detail,
        "enable_dossier_search": account.enable_dossier_search,
        "enable_zoom_feature": account.enable_zoom_feature,
        "enable_debug_document": account.enable_debug_document,
        "enable_download_dossier": account.enable_download_dossier,
        "enable_download_document": account.enable_download_document,
        "enable_icons_on_page_view": account.enable_icons_on_page_view,
        "enable_uploading_files": account.enable_uploading_files,
        "enable_drag_and_drop_in_page_view": account.enable_drag_and_drop_in_page_view,
        "enable_rendering_structure_tab": account.enable_rendering_structure_tab,
        "enable_hovered_section_on_page_view": account.enable_hovered_section_on_page_view,
        "enable_rendering_hurdles_tab": account.enable_rendering_hurdles_tab,
        "enable_area_calculator": account.enable_area_calculator,
        "enable_semantic_document_confidence": account.enable_semantic_document_confidence,
        "enable_semantic_document_export": account.enable_semantic_document_export,
        "enable_bekb_export": account.enable_bekb_export,
        "enable_bekb_automatic_collateral": account.enable_bekb_automatic_collateral,
        "enable_button_open_in_new_tab": account.enable_button_open_in_new_tab,
        "enable_rendering_photos_tab": account.enable_rendering_photos_tab,
        "enable_rendering_structure_details_tab": account.enable_rendering_structure_details_tab,
        "enable_rendering_bekb_mortgage_archiving_tab": account.enable_rendering_bekb_mortgage_archiving_tab,
        "enable_button_dossier_settings": account.enable_button_dossier_settings,
        "enable_button_dossier_notes": account.enable_button_dossier_notes,
        "enable_button_download": account.enable_button_download,
        "enable_document_upload": account.enable_document_upload,
        "enable_documents_delta_view": account.enable_documents_delta_view,
        "enable_semantic_page_image_lazy_loading": account.enable_semantic_page_image_lazy_loading,
        "enable_rendering_plans_tab": account.enable_rendering_plans_tab,
        "enable_real_estate_properties": account.enable_real_estate_properties,
        "enable_dossier_assignment": account.enable_dossier_assignment,
        "enable_dossier_assignment_to_someone_else": account.enable_dossier_assignment_to_someone_else,
        "navigation_strategy": account.navigation_strategy,
        "is_bekb": "BEKBDossierProperties"
        in map(attrgetter("__name__"), apps.get_models()),
        "state_machine": (
            request.auth.account.active_work_status_state_machine.uuid
            if request.auth.account.active_work_status_state_machine
            else None
        ),
        "frontend_theme": account.frontend_theme,
        "photo_album_docx_template": account.photo_album_docx_template,
        "instructions_menu_key": account.instructions_menu_key,
        "active_doc_check": account.active_doc_check_id,
        "enable_form_tab": account.enable_form_tab,
        "enable_semantic_document_splitting": account.enable_semantic_document_splitting,
        "enable_automatic_semantic_document_splitting": account.enable_automatic_semantic_document_splitting,
        "dossier_access_check_error_component": account.dossier_access_check_error_component,
        "enable_custom_semantic_document_date": account.enable_custom_semantic_document_date,
        "enable_semantic_document_annotations": account.enable_semantic_document_annotations,
        "enable_download_extraction_excel": account.enable_download_extraction_excel,
        "enable_dossier_permission": account.enable_dossier_permission,
        "enable_download_metadata_json": account.enable_download_metadata_json,
        "document_download_ui_add_uuid_suffix": account.document_download_ui_add_uuid_suffix,
    }


@core_router.get(
    "/account/available-roles",
    response=DossierAvailableRolesSchema,
    url_name="account-available-roles",
    description="Get all roles without the account users that can be used as a filter",
)
def get_dossier_account_available_roles(request: JWTAuthRequest):
    account = request.auth.account
    available_dossier_roles = DossierRole.objects.filter(
        account=account, show_separate_filter=True
    )
    all_roles = []
    for role in available_dossier_roles:
        all_roles.append(
            schemas.DossierRole(
                uuid=role.uuid,
                account=role.account.name,
                key=role.key,
                name_de=role.name_de,
                name_en=role.name_en,
                name_fr=role.name_fr,
                name_it=role.name_it,
                user_selectable=role.user_selectable,
                show_separate_filter=role.show_separate_filter,
                users=[],
            )
        )

    return DossierAvailableRolesSchema(available_dossier_roles=all_roles)


@core_router.get(
    "/account/available-roles/{role_uuid}/filter-users",
    response=DossierRoleFilterUsersSchema,
    url_name="account-available-role-users",
    description="Get all roles and account users that can be used as a filter",
)
def get_dossier_account_role_users(
    request: JWTAuthRequest, role_uuid: UUID, query: Optional[str] = None
):
    account = request.auth.account
    dossier_role = DossierRole.objects.filter(account=account, uuid=role_uuid).first()

    if dossier_role is None:
        return DossierRoleFilterUsersSchema(filter_users=[])

    user_involvements = (
        UserInvolvement.objects.select_related("user__user")
        .filter(role=dossier_role)
        .distinct("user_id")
    )
    filter_users = []
    for involvement in user_involvements:
        filter_users.append(
            FilterUser(
                username=involvement.user.user.username,
                first_name=involvement.user.user.first_name,
                last_name=involvement.user.user.last_name,
            )
        )
    return DossierRoleFilterUsersSchema(filter_users=filter_users)


@core_router.get(
    "/{dossier_uuid}/assignable_users",
    response=DossierAssignableUsersSchema,
    url_name="dossier-assignable-users",
    description="Get users that can be assigned to an account, query parameter filters for firstname, lastname and "
    "username",
)
def get_dossier_assignable_users(
    request: JWTAuthRequest, dossier_uuid: UUID, query: Optional[str] = None
):
    # get_dossier_from_request_with_access_check doesn't work if bekbdossierproperties__pers=False,
    # so we can't look up the dossier assignments for it
    # we could do the following
    # dossier = get_object_or_404(
    #     Dossier, uuid=dossier_uuid, account=request.auth.account
    # )
    # However, I expect, that only pers is ever allowed to assign users in the frontend
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    account_users = DossierUser.objects.filter(account=dossier.account).order_by(
        "user__first_name", "user__last_name", "user__username"
    )

    # Handle BEKB specific case
    if BEKBDossierProperties.objects.filter(
        dossier=dossier,
        pers=True,
    ).exists():
        account_users = account_users.filter(
            user__groups__name=f"BEKB/{dossier.account.key}/PERS"
        )

    if query:
        query_parts = query.split()  # splits the query string into words
        if len(query_parts) >= 2:
            firstname, lastname = query_parts[0], query_parts[1]
            account_users = account_users.filter(
                Q(
                    user__first_name__icontains=firstname,
                    user__last_name__icontains=lastname,
                ),
                Q(
                    user__first_name__icontains=lastname,
                    user__last_name__icontains=firstname,
                )
                | Q(user__first_name__icontains=query)
                | Q(user__last_name__icontains=query)
                | Q(user__username__icontains=query),
            )
        else:
            account_users = account_users.filter(
                Q(user__first_name__icontains=query)
                | Q(user__last_name__icontains=query)
                | Q(user__username__icontains=query)
            )

    assignable_users_list = []

    for account_user in account_users:
        assignable_users_list.append(
            AccountUser(
                username=account_user.user.username,
                firstname=account_user.user.first_name,
                lastname=account_user.user.last_name,
                account_user_uuid=account_user.uuid,
            )
        )

    return DossierAssignableUsersSchema(assignable_users_list=assignable_users_list)


@core_router.put(
    "/account/{account_key}/document-categories",
    response={401: Message, 404: Message, 202: None},
)
def update_document_categories(
    request, account_key: str, document_categories: List[schemas.DocumentCategoryLoad]
):
    if not request.is_internal:
        return 401, {"detail": "Unauthorized"}

    account = get_object_or_404(Account, key=account_key)

    load_document_categories(account, document_categories)
    return 202, ""


@core_router.get("/jwt", response=Any, auth=JWTAuth())
def show_jwt(request):
    token = request.headers["Authorization"].split(" ")[1]
    payload = Auth.jwt_auth(token)
    return {"data from token": payload}


@dossier_router.post(
    "/{dossier_uuid}/export_page_objects",
    response={
        codes_4xx: Message,
        200: dossier_zipper_schemas.ExportPageObjectsResponse,
    },
    url_name="export-page-object-images",
    auth=async_auth,
)
async def export_page_object_images(
    request,
    dossier_uuid,
    request_body: dossier_zipper_schemas.ExportPageImageRequestBody,
):
    """`
    This endpoint is async  it uses Custom authentication method async_auth , that does only verify and  decode the
    token

     besides , it uses a custom method for fetching the Dossier user

     It also uses async version of the dossier access check

    """

    # do an access check on the dossier
    await get_dossier_from_request_with_access_check_async(request, dossier_uuid)

    return await generate_images(dossier_uuid, request_body)


@dossier_router.patch(
    "/{dossier_uuid}/update_note", response=schemas.DossierNoteResponse
)
def update_dossier_note(
    request, dossier_uuid, request_body: schemas.DossierNoteRequestBody
):
    # TODO: add test for this api call
    dossier = get_writable_dossier_from_request_with_access_check(request, dossier_uuid)

    if request_body.dossier_note != dossier.note:
        dossier.note = request_body.dossier_note
        dossier.save()

    return schemas.DossierNoteResponse(
        dossier_uuid=dossier.uuid, dossier_note=dossier.note
    )


@dossier_router.patch(
    "/{dossier_uuid}/{semantic_page_uuid}/{page_object_uuid}/update_confidence",
    response=schemas.PageObjectFullApiData,
)
def update_page_object_confidence(
    request,
    dossier_uuid: UUID,
    semantic_page_uuid: UUID,
    page_object_uuid: UUID,
    request_body: schemas.UpdatePageObjectConfidenceRequest,
):
    dossier = get_writable_dossier_from_request_with_access_check(request, dossier_uuid)
    semantic_page: SemanticPage = get_object_or_404(
        SemanticPage, uuid=semantic_page_uuid
    )
    assert_semantic_document_is_writable(semantic_page.semantic_document)

    page_object = get_page_object_from_api(
        dossier, page_object_uuid, semantic_page_uuid
    )
    dossier_helpers.update_page_object_confidence(page_object, request_body)

    return dossier_helpers.prepare_page_object(page_object)


@dossier_router.get(
    "/{dossier_uuid}/default-document-categories",
    response=List[schemas.DocumentCategoryTranslated],
)
def get_default_document_types(request: JWTAuthRequest, dossier_uuid: UUID):
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    default_document_categories = [
        "PERSON_MISC",
        "FINANCE_MISC",
        "PENSION_MISC",
        "PROPERTY_MISC",
        "FINANCING_MISC",
    ]
    document_categories = DocumentCategory.objects.filter(
        name__in=default_document_categories, account=dossier.account
    ).order_by("id")

    return [
        schemas.DocumentCategoryTranslated(
            name=document_category.name,
            id=document_category.id,
            translation=document_category.translated(dossier.lang),
        )
        for document_category in document_categories
    ]


@dossier_router.get(
    "/{dossier_uuid}/next-possible-states",
    response=List[smgmt_schema.PossibleTransition],
)
def get_next_possible_work_states(request: JWTAuthRequest, dossier_uuid: UUID):
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    if dossier.account.active_work_status_state_machine is None:
        return []

    context = create_dossier_state_context(
        dossier=dossier, is_user=True, is_system=False
    )
    return calculate_next_possible_states(context, dossier.work_status)


@dossier_router.patch(
    "/{dossier_uuid}/update_bekb_business_case",
    response=schemas.DossierBekbBusinessCaseResponse,
)
def update_dossier_bekb_business_case(
    request: JWTAuthRequest,
    dossier_uuid,
    request_body: schemas.DossierBekbBusinessCaseRequestBody,
):
    # TODO: add test for this api call
    dossier = get_writable_dossier_from_request_with_access_check(request, dossier_uuid)

    bekb_dossier_properties = get_object_or_404(BEKBDossierProperties, dossier=dossier)

    dossier_helpers.delete_document_collateral_assigment(dossier)
    business_case = BusinessCase.objects.filter(
        business_number=request_body.business_number
    ).first()

    bekb_dossier_properties.business_case = business_case

    bekb_dossier_properties.save()
    return schemas.DossierBekbBusinessCaseResponse(
        dossier_uuid=dossier_uuid, business_number=request_body.business_number
    )


@dossier_router.get(
    "/{dossier_uuid}/check_access",
    url_name="check-dossier-access",
    response={200: schemas.DossierAccessGrant, 404: Message, 401: Message},
    description="Check if the user has access to the dossier, return 40x if not",
)
def check_dossier_access(request: JWTAuthRequest, dossier_uuid: UUID):
    requested = timezone.now()

    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    return schemas.DossierAccessGrant(
        dossier_uuid=dossier.uuid,
        requested=requested,
        issued=timezone.now(),
        expires=timezone.now() + timedelta(minutes=9),
        has_access=True,
    )


@dossier_router.get(
    "/{dossier_uuid}/token",
    response={401: Message, 200: str},
    url_name="dossier-access-token",
)
def get_dossier_grant_token(request, dossier_uuid: UUID):
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)
    return get_grant_token_for_dossier(dossier)


@dossier_router.get(
    "/{dossier_uuid}/basics",
    response={200: schemas.SemanticDocumentBasicsList, 400: Message},
    url_name="semantic_document_basics",
)
def get_semantic_document_basics(
    request: JWTAuthRequest,
    dossier_uuid: UUID,
    updated_from_date: Optional[datetime] = None,
    updated_to_date: Optional[datetime] = None,
):
    """
    Retrieve a list of semantic document basic information associated with a specific dossier.

    Parameters:
    - request: The HTTP request object.
    - dossier_uuid (UUID): The UUID of the dossier for which semantic documents are to be retrieved.
    - from_date (Optional[str]): Start date for filtering semantic documents based on last update.
    - to_date (Optional[str]): End date for filtering semantic documents based on last update.

    Returns:
    - A `SemanticDocumentBasicsList` schema object containing the UUID of the dossier and a list of
      its associated semantic documents basic information without semantic page objects.
    """

    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    semantic_documents = SemanticDocument.objects.filter(dossier=dossier).values(
        "uuid",
        "document_category__name",
        "confidence_level",
        "confidence_formatted",
        "confidence_value",
        "title_custom",
        "title_suffix",
        "last_page_change_date",
        "last_entity_change_date",
        "access_mode",
        "external_semantic_document_id",
    )

    if updated_from_date and updated_to_date:
        if updated_from_date > updated_to_date:
            return 400, {"detail": "Invalid date range"}
        semantic_documents = semantic_documents.filter(
            updated_at__gte=updated_from_date, updated_at__lte=updated_to_date
        )
    elif updated_from_date:
        semantic_documents = semantic_documents.filter(
            updated_at__gte=updated_from_date
        )
    elif updated_to_date:
        semantic_documents = semantic_documents.filter(updated_at__lte=updated_to_date)

    return schemas.SemanticDocumentBasicsList(
        dossier_uuid=dossier.uuid, semantic_documents=list(semantic_documents)
    )


@dossier_router.get(
    "/{dossier_uuid}/aggregate-page-objects",
    response=List[schemas.PageObjectApiDataWithUUID],
    url_name="dossier-aggregate-page-objects",
    description="Get aggregate page objects for a specific dossier. "
    "Aggregate means that page_object.key is unique per semantic document",
)
def get_dossier_aggregate_page_objects(request, dossier_uuid: UUID):

    dossier: Dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    return get_aggregate_page_objects_for_dossier(dossier=dossier)


@dossier_router.get(
    "/{dossier_uuid}/all-page-objects",
    response=List[schemas.PageObjectApiDataWithUUID],
    url_name="dossier-all-page-objects",
    description="Get all page objects for a specific dossier.",
)
def get_dossier_all_page_objects(request, dossier_uuid: UUID):

    dossier: Dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    dossier_page_objects = (
        SemanticPagePageObject.objects.filter(semantic_page__dossier=dossier)
        .select_related(
            "page_object",
            "semantic_page",
            "semantic_page__semantic_document",
            "page_object__key",
            "page_object__type",
            "page_object__processed_page",
        )
        .order_by(
            "semantic_page__semantic_document",
            "semantic_page__number",
            "page_object__top",
            "page_object__right",
        )
    )

    return [
        dossier_helpers.prepare_page_object_v2(page_object)
        for page_object in dossier_page_objects
    ]


@dossier_router.get(
    "/{dossier_uuid}/semantic-pages",
    response=List[schemas.SemanticPageNoPageObjects],
    url_name="dossier-semantic-pages",
    description="Light weight api to get all semantic pages a specific dossier.",
)
def get_dossier_semantic_pages(request, dossier_uuid: UUID):

    get_dossier_from_request_with_access_check(request, dossier_uuid)

    semantic_pages_list = [
        {
            **serialize_semantic_page(semantic_page),
        }
        for semantic_page in get_semantic_pages(
            dossier=dossier_uuid, show_soft_deleted=False
        )
    ]

    return semantic_pages_list
