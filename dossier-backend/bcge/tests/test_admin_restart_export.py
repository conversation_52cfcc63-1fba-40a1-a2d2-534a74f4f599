from unittest.mock import Mock

import pytest
from django.contrib.auth import get_user_model
from django.test import RequestFactory

from semantic_document.models import SemanticDocument
from semantic_document.services import set_semantic_documents_ready_for_export
from semantic_document.services_admin import (
    admin_reset_restart_semantic_document_export,
)
from statemgmt.configurations.semantic_document_state_machine import (
    SemanticDocumentState,
)
from workers.models import SemanticDocumentExport

pytestmark = pytest.mark.django_db

User = get_user_model()


def test_admin_restart_export_bcge_success(
    prepare_data_export, bcge_authenticated_client, mocker
):
    """Test that admin restart export works for BCGE accounts."""

    external_dossier_id, semantic_document, mock_dispatch_publish_request = (
        prepare_data_export
    )

    dossier = semantic_document.dossier

    semantic_documents = SemanticDocument.objects.filter(
        work_status__key=SemanticDocumentState.IN_FRONT_OFFICE.value,
        work_status__state_machine=dossier.account.active_semantic_document_work_status_state_machine,
        dossier=dossier,
    )

    uuids = set_semantic_documents_ready_for_export(semantic_documents)
    assert len(uuids) == 1  # one sem doc in dossier

    mock_dispatch_publish_request.assert_called()

    semantic_document_export = SemanticDocumentExport.objects.get(uuid=str(uuids[0]))

    assert semantic_document_export.file
    assert semantic_document_export.file.get_fast_url()

    semantic_document.refresh_from_db()

    # Verify initial state
    assert (
        semantic_document.work_status.key
        == SemanticDocumentState.EXPORT_AVAILABLE.value
    )
    assert SemanticDocumentExport.objects.filter(
        semantic_document=semantic_document
    ).exists()

    # Create queryset and request for admin action
    queryset = SemanticDocument.objects.filter(uuid=semantic_document.uuid)
    factory = RequestFactory()
    request = factory.post("/admin/")
    request.user, _ = User.objects.get_or_create(
        username="admin_test_user", defaults={"password": "testpass"}
    )
    request._messages = Mock()  # Mock Django messages framework

    # Call the admin restart function
    admin_reset_restart_semantic_document_export(request, queryset)

    # Refresh the semantic document from database
    semantic_document.refresh_from_db()

    # The document should be in EXPORT_AVAILABLE state after restart
    assert (
        semantic_document.work_status.key
        == SemanticDocumentState.EXPORT_AVAILABLE.value
    )
    assert semantic_document.access_mode == "read_only"

    # Verify new export was created
    assert SemanticDocumentExport.objects.filter(
        semantic_document=semantic_document
    ).exists()

    # Verify the mock was called (export process was triggered)
    mock_dispatch_publish_request.assert_called()


def test_admin_restart_export_reset_phase_works(
    prepare_data_export, bcge_authenticated_client, mocker
):
    """Test that the reset phase of admin restart export works correctly."""

    external_dossier_id, semantic_document, mock_dispatch_publish_request = (
        prepare_data_export
    )

    dossier = semantic_document.dossier

    semantic_documents = SemanticDocument.objects.filter(
        work_status__key=SemanticDocumentState.IN_FRONT_OFFICE.value,
        work_status__state_machine=dossier.account.active_semantic_document_work_status_state_machine,
        dossier=dossier,
    )

    uuids = set_semantic_documents_ready_for_export(semantic_documents)
    assert len(uuids) == 1  # one sem doc in dossier

    mock_dispatch_publish_request.assert_called()

    semantic_document_export = SemanticDocumentExport.objects.get(uuid=str(uuids[0]))

    assert semantic_document_export.file
    assert semantic_document_export.file.get_fast_url()

    semantic_document.refresh_from_db()

    # Verify initial state
    assert (
        semantic_document.work_status.key
        == SemanticDocumentState.EXPORT_AVAILABLE.value
    )
    assert SemanticDocumentExport.objects.filter(
        semantic_document=semantic_document
    ).exists()

    existing_export = SemanticDocumentExport.objects.get(
        semantic_document=semantic_document
    )

    # Create queryset and request for admin action
    queryset = SemanticDocument.objects.filter(uuid=semantic_document.uuid)
    factory = RequestFactory()
    request = factory.post("/admin/")
    request.user, _ = User.objects.get_or_create(
        username="admin_test_user2", defaults={"password": "testpass"}
    )
    request._messages = Mock()  # Mock Django messages framework

    # Mock the restart phase to fail, so we can test just the reset phase
    mocker.patch(
        "semantic_document.services_admin.set_semantic_documents_ready_for_export",
        side_effect=Exception("Simulated restart failure"),
    )

    # Call the admin restart function - it should fail at restart but reset should work
    with pytest.raises(Exception, match="Simulated restart failure"):
        admin_reset_restart_semantic_document_export(request, queryset)

    # Refresh the semantic document from database
    semantic_document.refresh_from_db()

    # Verify the reset phase worked - document should be in IN_FRONT_OFFICE state
    assert (
        semantic_document.work_status.key == SemanticDocumentState.IN_FRONT_OFFICE.value
    )

    # Verify the existing export was deleted during reset
    assert not SemanticDocumentExport.objects.filter(uuid=existing_export.uuid).exists()
