from typing import List, Dict, Optional
import structlog

from cdp.repositories import (
    FieldRepository,
    FieldContextRepository,
)
from cdp.field_context import (
    FieldContextHandler,
    FieldCtxInRecommendationSourceStrategy,
    CDP_FIELD_CONTEXT_STRATEGIES,
)

logger = structlog.get_logger()


class FieldContextService:
    def __init__(
        self, field_repo: FieldRepository, field_context_repo: FieldContextRepository
    ):
        self.field_repo = field_repo
        self.field_context_repo = field_context_repo

    def apply_field_context(
        self,
        recommendations: List,
        field_context: Optional[Dict],
        field_definition,
    ) -> List:
        """Filter recommendations based on field context.

        This method uses the FieldContextHandler to apply field context filtering.
        It supports both the simplified approach (checking if field values match context)
        and the advanced approach (using the field context strategy).
        """
        if not field_context:
            return recommendations

        # Get relevant field contexts from the repository
        relevant_fields_for_context = (
            self.field_context_repo.get_relevant_field_contexts(field_definition)
        )

        # If no relevant fields are defined, use the simplified approach
        if not relevant_fields_for_context:
            return recommendations

        # Use the FieldContextHandler for advanced filtering
        field_context_handler = FieldContextHandler(
            field_definition=field_definition,
        )

        # Get the appropriate strategy based on the field definition's context strategy
        strategy_key = self.field_context_repo.get_field_context_strategy(
            field_definition
        )
        field_context_strategy = CDP_FIELD_CONTEXT_STRATEGIES.model_dump().get(
            strategy_key, FieldCtxInRecommendationSourceStrategy
        )()

        # Apply the field context using the handler
        return field_context_handler.apply_field_context(
            recommendations, field_context, field_context_strategy
        )
