import json

import pytest

from cdp.api import AuthBearer


@pytest.fixture
def auth_bearer():
    return AuthBearer()


@pytest.fixture
def invalid_token():
    # Create a random invalid token
    return "invalid-token"


@pytest.mark.django_db
def test_authenticate_valid_token(
    rf, auth_bearer, valid_token, dossier_for_default_account
):
    request = rf.post(
        "/cdp-api/grouped_recommendations/", HTTP_AUTHORIZATION=f"Bearer {valid_token}"
    )
    jwt_object = auth_bearer.authenticate(request, valid_token)
    jwt_claims = json.loads(jwt_object.claims)
    assert jwt_claims["iss"] == "dms"
    assert jwt_claims["aud"] == "cdp"
    assert jwt_claims["dossier_uuid"] == str(dossier_for_default_account[0].uuid)


@pytest.mark.django_db
def test_authenticate_invalid_token(rf, auth_bearer, invalid_token):
    request = rf.post(
        "/cdp-api/grouped_recommendations/",
        HTTP_AUTHORIZATION=f"Bearer {invalid_token}",
    )
    jwt = auth_bearer.authenticate(request, invalid_token)

    assert jwt is None  # JWT should not be valid


@pytest.mark.django_db
def test_authenticate_expired_token(rf, auth_bearer, expired_token):
    request = rf.get(
        "/cdp-api/field_definitions/", HTTP_AUTHORIZATION=f"Bearer {expired_token}"
    )
    jwt = auth_bearer.authenticate(request, expired_token)
    assert jwt is None  # Expired JWT should not be authenticated
