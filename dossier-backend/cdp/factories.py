from factory.django import DjangoModelFactory
import factory

from cdp.models import (
    FieldSet,
    FieldDefinition,
    AssignedField,
    PageObjectType,
    RelevantPageObject,
    DocumentCategory,
    ReturnType,
    RelevantSemanticPage,
    RelevantSemanticDocument,
)

from dossier.factories import (
    SemanticDocumentFactory,
    PageObjectFactory,
    SemanticPageFactory,
    PageObjectTitleFactory,
    SemanticPagePageObjectFactory,
    AccountFactory,
)
from dossier.models import Dossier
from dossier.models import DocumentCategory as DossierDocumentCategory


class FieldSetFactory(DjangoModelFactory):
    class Meta:
        model = FieldSet

    key = "hd_internal"


class ReturnTypeFactory(DjangoModelFactory):
    class Meta:
        model = ReturnType

    key = "string"


class FieldDefinitionFactory(DjangoModelFactory):
    class Meta:
        model = FieldDefinition

    key = factory.Sequence(lambda n: f"field_{n}")
    flavour = "hd"


class AssignedFieldFactory(DjangoModelFactory):
    class Meta:
        model = AssignedField

    field_set = factory.SubFactory(FieldSetFactory)
    field_definition = factory.SubFactory(FieldDefinitionFactory)


class PageObjectTypeFactory(DjangoModelFactory):
    class Meta:
        model = PageObjectType

    key = factory.Sequence(lambda n: f"PageObject_{n}")


class RelevantPageObjectFactory(DjangoModelFactory):
    class Meta:
        model = RelevantPageObject

    field_definition = factory.SubFactory(FieldDefinitionFactory)
    page_object = factory.SubFactory(PageObjectTypeFactory)


class DocumentCategoryFactory(DjangoModelFactory):
    class Meta:
        model = DocumentCategory

    key = factory.Sequence(lambda n: f"DocumentCategory_{n}")


#
# --- No PriorityMappingFactory anymore ---
#


def sample_cdp_dossier(
    dossier: Dossier, field_set: str, hints: bool = False
) -> Dossier:

    # 1) Create related objects
    fs = FieldSetFactory(key=field_set)
    rt = ReturnTypeFactory(key="string")
    field_definition1 = FieldDefinitionFactory(key="field_0", return_type=rt)
    field_definition2 = FieldDefinitionFactory(key="field_1", return_type=rt)
    AssignedFieldFactory(field_set=fs, field_definition=field_definition1)
    AssignedFieldFactory(field_set=fs, field_definition=field_definition2)

    page_object_types = []
    page_object_titles = []
    document_categories = []

    # 2) Create some PageObjectTypes, PageObjectTitles, DocumentCategories
    for _ in range(5):
        pot = PageObjectTypeFactory()
        page_object_types.append(pot)
        pot_title = PageObjectTitleFactory(key=pot.key)
        page_object_titles.append(pot_title)
        doc_cat = DocumentCategoryFactory()
        document_categories.append(doc_cat)

    # === FIELD DEFINITION 1===
    # RelevantPageObject
    rpo1 = RelevantPageObjectFactory(
        field_definition=field_definition1, page_object=page_object_types[0]
    )
    rpo2 = RelevantPageObjectFactory(
        field_definition=field_definition1, page_object=page_object_types[1]
    )

    if not hints:
        rpo1.generic_priority_mapping.create(
            document_category=document_categories[0], priority=1
        )
        rpo2.generic_priority_mapping.create(
            document_category=document_categories[1], priority=2
        )

    if hints:
        # RelevantSemanticPage for field_definition1
        rsp1 = RelevantSemanticPage.objects.create(
            field_definition=field_definition1,
            relevant_object_type="SEMANTIC_PAGE",
        )
        rsp1.generic_priority_mapping.create(
            document_category=document_categories[0], priority=1
        )
        rsp1.generic_priority_mapping.create(
            document_category=document_categories[1], priority=2
        )

        # RelevantSemanticDocument for field_definition1
        rsd1 = RelevantSemanticDocument.objects.create(
            field_definition=field_definition1,
            relevant_object_type="SEMANTIC_DOCUMENT",
        )
        rsd1.generic_priority_mapping.create(
            document_category=document_categories[0], priority=1
        )
        rsd1.generic_priority_mapping.create(
            document_category=document_categories[1], priority=2
        )

    # === FIELD DEFINITION 2 ===
    # RelevantPageObject
    rpo3 = RelevantPageObjectFactory(
        field_definition=field_definition2, page_object=page_object_types[2]
    )
    rpo4 = RelevantPageObjectFactory(
        field_definition=field_definition2, page_object=page_object_types[3]
    )
    rpo5 = RelevantPageObjectFactory(
        field_definition=field_definition2, page_object=page_object_types[4]
    )

    if not hints:
        rpo3.generic_priority_mapping.create(
            document_category=document_categories[2], priority=1
        )
        rpo4.generic_priority_mapping.create(
            document_category=document_categories[3], priority=2
        )
        rpo5.generic_priority_mapping.create(
            document_category=document_categories[4], priority=2
        )
    if hints:
        # RelevantSemanticPage for field_definition2
        rsp2 = RelevantSemanticPage.objects.create(
            field_definition=field_definition2,
            relevant_object_type="SEMANTIC_PAGE",
        )
        # GenericPriorityMappings for these
        rsp2.generic_priority_mapping.create(
            document_category=document_categories[2], priority=1
        )
        rsp2.generic_priority_mapping.create(
            document_category=document_categories[3], priority=2
        )
        rsp2.generic_priority_mapping.create(
            document_category=document_categories[4], priority=2
        )

        # RelevantSemanticDocument for field_definition2
        rsd2 = RelevantSemanticDocument.objects.create(
            field_definition=field_definition2,
            relevant_object_type="SEMANTIC_DOCUMENT",
        )

        # GenericPriorityMappings for these
        rsd2.generic_priority_mapping.create(
            document_category=document_categories[2], priority=1
        )
        rsd2.generic_priority_mapping.create(
            document_category=document_categories[3], priority=2
        )
        rsd2.generic_priority_mapping.create(
            document_category=document_categories[4], priority=2
        )

    # 3) Create sample semantic documents/pages
    confidence_values = [0.9, 0.95, 0.8, 0.9, 0.95]
    for i in range(1, 3):
        semantic_document = SemanticDocumentFactory(
            dossier=dossier,
            document_category=DossierDocumentCategory.objects.get_or_create(
                name=document_categories[i - 1].key, account=AccountFactory()
            )[0],
        )
        semantic_page = SemanticPageFactory(
            dossier=dossier, semantic_document=semantic_document, page_number=1
        )
        page_object = PageObjectFactory(
            key=page_object_titles[i - 1],
            processed_page=semantic_page.processed_page,
            value=f"value_{i}",
            confidence_value=confidence_values[i - 1],
        )
        SemanticPagePageObjectFactory(
            page_object=page_object, semantic_page=semantic_page
        )

    for i in range(3, 6):
        semantic_document = SemanticDocumentFactory(
            dossier=dossier,
            document_category=DossierDocumentCategory.objects.get_or_create(
                name=document_categories[i - 1].key, account=AccountFactory()
            )[0],
        )
        semantic_page = SemanticPageFactory(
            dossier=dossier, semantic_document=semantic_document, page_number=1
        )
        page_object = PageObjectFactory(
            key=page_object_titles[i - 1],
            processed_page=semantic_page.processed_page,
            value=f"value_{i}",
            confidence_value=confidence_values[i - 1],
        )
        SemanticPagePageObjectFactory(
            page_object=page_object, semantic_page=semantic_page
        )

    return dossier


def sample_dossier_cdp_with_multiple_occurences(
    dossier: Dossier, field_set: str
) -> Dossier:

    fs = FieldSetFactory(key=field_set)
    field_definition1 = FieldDefinitionFactory(key="field_0")
    field_definition2 = FieldDefinitionFactory(key="field_1", is_editable_hint=False)
    AssignedFieldFactory(field_set=fs, field_definition=field_definition1)
    AssignedFieldFactory(field_set=fs, field_definition=field_definition2)

    page_object_types = []
    page_object_titles = []
    document_categories = []

    for _ in range(5):
        pot = PageObjectTypeFactory()
        page_object_types.append(pot)
        pot_title = PageObjectTitleFactory(key=pot.key)
        page_object_titles.append(pot_title)
        doc_cat = DocumentCategoryFactory()
        document_categories.append(doc_cat)

    # field_definition1
    rpo1 = RelevantPageObjectFactory(
        field_definition=field_definition1, page_object=page_object_types[0]
    )
    rpo2 = RelevantPageObjectFactory(
        field_definition=field_definition1, page_object=page_object_types[1]
    )

    rpo1.generic_priority_mapping.create(
        document_category=document_categories[0], priority=1
    )
    rpo2.generic_priority_mapping.create(
        document_category=document_categories[1], priority=2
    )

    # field_definition2
    rpo3 = RelevantPageObjectFactory(
        field_definition=field_definition2, page_object=page_object_types[2]
    )
    rpo4 = RelevantPageObjectFactory(
        field_definition=field_definition2, page_object=page_object_types[3]
    )
    rpo5 = RelevantPageObjectFactory(
        field_definition=field_definition2, page_object=page_object_types[4]
    )

    rpo3.generic_priority_mapping.create(
        document_category=document_categories[2], priority=1
    )
    rpo4.generic_priority_mapping.create(
        document_category=document_categories[3], priority=2
    )
    rpo5.generic_priority_mapping.create(
        document_category=document_categories[4], priority=2
    )

    confidence_values = [0.9, 0.95, 0.8, 0.9, 0.95]
    for i in range(1, 3):
        for _ in range(3):
            semantic_document = SemanticDocumentFactory(
                dossier=dossier,
                document_category=DossierDocumentCategory.objects.get_or_create(
                    name=document_categories[i - 1].key, account=AccountFactory()
                )[0],
            )
            semantic_page = SemanticPageFactory(
                dossier=dossier, semantic_document=semantic_document, page_number=1
            )
            page_object = PageObjectFactory(
                key=page_object_titles[i - 1],
                processed_page=semantic_page.processed_page,
                value=f"value_{i}",
                confidence_value=confidence_values[i - 1],
            )
            SemanticPagePageObjectFactory(
                page_object=page_object, semantic_page=semantic_page
            )

    for i in range(3, 6):
        for _ in range(2):
            semantic_document = SemanticDocumentFactory(
                dossier=dossier,
                document_category=DossierDocumentCategory.objects.get_or_create(
                    name=document_categories[i - 1].key, account=AccountFactory()
                )[0],
            )
            semantic_page = SemanticPageFactory(
                dossier=dossier, semantic_document=semantic_document, page_number=1
            )
            page_object = PageObjectFactory(
                key=page_object_titles[i - 1],
                processed_page=semantic_page.processed_page,
                value=f"value_{i}",
                confidence_value=confidence_values[i - 1],
            )
            SemanticPagePageObjectFactory(
                page_object=page_object, semantic_page=semantic_page
            )

    return dossier
