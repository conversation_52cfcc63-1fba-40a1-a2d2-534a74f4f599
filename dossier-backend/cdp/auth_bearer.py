import structlog
from django.conf import settings
from django.http import HttpRequest
from jwcrypto.jwk import JW<PERSON>
from jwcrypto.jwt import JWT, JWTExpired
from ninja.security import HttpBearer

logger = structlog.get_logger()


class AuthBearer(HttpBearer):
    def __init__(self) -> None:
        super().__init__()

        self.private_key = JWK.from_json(settings.INSTANCE_SIGNING_KEY)

    def authenticate(self, request: HttpRequest, token: str):
        jwt = JWT()
        logger.info("dossier-access-token", token=token)
        try:
            jwt.deserialize(token, self.private_key)
            return jwt
        except JWTExpired as e:
            logger.warning("JWT token expired", error=str(e))
            return None
        except Exception as e:
            logger.error("Failed to authenticate", error=str(e))
            return None
