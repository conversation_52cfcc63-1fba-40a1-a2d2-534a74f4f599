import os
import time

from django.core.wsgi import get_wsgi_application

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "projectconfig.settings")
django_wgsi_app = get_wsgi_application()

from cdp.models import FieldDefinition
from cdp.schemas import NumRecommendationRequest
from cdp.services_legacy import get_num_recommendations
from dossier.models import Dossier


# get all the field definitions defined for the Field Set : hd_internal
def get_all_internal_field_definitions():
    field_definitions = FieldDefinition.objects.filter(
        assignedfield__field_set__key="hd_internal"
    )
    if field_definitions.exists():
        field_definition_list = [f.key for f in field_definitions]
        return field_definition_list
    else:
        return []


def get_num_recommendations_post_api_stats(n_dossiers=100):
    # get all dossiers from the db
    dossiers = Dossier.objects.all()
    num_dossiers = dossiers.count()
    print(f"Total number of dossiers: {num_dossiers}")

    # get the statistics on how long num_recommendations_post api takes for all the dossiers in the DB
    api_stats = []
    field_definition_list = get_all_internal_field_definitions()
    num_recommendations_request = NumRecommendationRequest(
        field_definitions=field_definition_list
    )
    for dossier in dossiers[:n_dossiers]:
        start_time = time.time()
        _ = get_num_recommendations(
            dossier.uuid, "hd_internal", num_recommendations_request
        )
        end_time = time.time()
        api_time = end_time - start_time
        api_stats.append(api_time)
        print(f"Time taken for dossier {dossier.uuid}: {api_time}")
    return api_stats


# run if main
if __name__ == "__main__":
    api_stats = get_num_recommendations_post_api_stats(n_dossiers=10)
    print(f"Average time taken : {sum(api_stats)/len(api_stats)}")
