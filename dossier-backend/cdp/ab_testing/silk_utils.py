"""
Utilities for integrating Django Silk with the A/B testing framework.

This module provides functions for tracking SQL queries using Django Silk.
"""

import functools
import time
from typing import Dict, Any, Callable, Optional
import uuid

from django.conf import settings
from django.http import HttpRequest
from django.db import connection
from django.test.utils import CaptureQueriesContext


def is_silk_enabled() -> bool:
    """Check if Django Silk is enabled."""
    return getattr(settings, "DEBUG_ENABLE_SILK", False)


# Conditional import and definition of silk_profile
if is_silk_enabled():
    from silk.profiling.profiler import silk_profile
    from silk.models import Request as SilkRequest
else:
    # Define a dummy silk_profile if Silk is not enabled
    # This ensures that the decorator exists but does nothing.
    def silk_profile(name: str = None, dynamic=False) -> Callable:
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs) -> Any:
                return func(*args, **kwargs)

            return wrapper

        return decorator

    SilkRequest = None  # Define SilkRequest as None when not enabled


def get_silk_request_for_http_request(
    http_request: HttpRequest,
) -> Optional[SilkRequest]:
    """Get the Silk request object for an HTTP request."""
    if not is_silk_enabled():
        return None

    # Silk stores the request in the META dictionary
    return http_request.META.get("silk_request")


def get_sql_query_count(silk_request: SilkRequest) -> int:
    """Get the number of SQL queries for a Silk request."""
    if not silk_request:
        return 0

    return len(silk_request.queries.all())


def get_sql_query_time(silk_request: SilkRequest) -> float:
    """Get the total time spent on SQL queries for a Silk request."""
    if not silk_request:
        return 0.0

    return sum(q.time for q in silk_request.queries.all())


def get_sql_query_stats(silk_request: SilkRequest) -> Dict[str, Any]:
    """Get statistics about SQL queries for a Silk request."""
    if not silk_request:
        # If no Silk request is available, use CaptureQueriesContext instead
        return {
            "query_count": 0,
            "total_time": 0.0,
            "avg_time": 0.0,
            "silk_enabled": False,
            "silk_request": False,
        }

    queries = silk_request.queries.all()
    query_count = len(queries)
    total_time = sum(q.time for q in queries)
    avg_time = total_time / query_count if query_count > 0 else 0.0

    return {
        "query_count": query_count,
        "total_time": total_time,
        "avg_time": avg_time,
        "silk_enabled": True,
        "silk_request": True,
    }


def with_silk_profile(name: str = None) -> Callable:
    """
    Decorator to profile a function with Silk.

    If Silk is not enabled, this decorator does nothing.
    """

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if not is_silk_enabled():
                return func(*args, **kwargs)

            profile_name = name or f"{func.__module__}.{func.__name__}"
            with silk_profile(name=profile_name):
                return func(*args, **kwargs)

        return wrapper

    return decorator


def get_direct_sql_query_stats() -> Dict[str, Any]:
    """
    Get SQL query statistics using Django's CaptureQueriesContext.
    This works even outside of HTTP request contexts.
    """
    # We need to execute a dummy query to capture statistics
    # This is because CaptureQueriesContext only captures queries executed within its context
    with CaptureQueriesContext(connection) as context:
        # Execute a simple query to get the current time
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")

        queries = context.captured_queries
        query_count = len(queries)
        total_time = sum(float(q.get("time", 0)) for q in queries)
        avg_time = total_time / query_count if query_count > 0 else 0.0

        return {
            "query_count": query_count,
            "total_time": total_time,
            "avg_time": avg_time,
            # Don't include the actual queries to keep output clean
            "capture_queries_context": True,
        }


class SilkProfiler:
    """
    Context manager for profiling code blocks with Silk and SQL query tracking.

    This profiler works both with and without Silk enabled, and uses Django's
    CaptureQueriesContext for SQL query tracking.
    """

    def __init__(self, name: str = None):
        """
        Initialize the profiler.

        Args:
            name: Name of the profile. If not provided, a random name will be generated.
        """
        self.name = name or f"profile-{uuid.uuid4()}"
        self.start_time = None
        self.end_time = None
        self.enabled = is_silk_enabled()
        self.query_context = None

    def __enter__(self):
        """Enter the context manager."""
        # Start SQL query capture
        self.query_context = CaptureQueriesContext(connection)
        self.query_context.__enter__()

        # Start timing
        self.start_time = time.time()

        # Start Silk profiling if enabled
        if self.enabled:
            silk_profile(name=self.name).__enter__()

        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit the context manager."""
        # End timing
        self.end_time = time.time()

        # End Silk profiling if enabled
        if self.enabled:
            silk_profile(name=self.name).__exit__(exc_type, exc_val, exc_tb)

        # End SQL query capture
        self.query_context.__exit__(exc_type, exc_val, exc_tb)

    @property
    def elapsed_time(self) -> float:
        """Get the elapsed time in seconds."""
        if self.start_time is None or self.end_time is None:
            return 0.0
        return self.end_time - self.start_time

    @property
    def sql_stats(self) -> Dict[str, Any]:
        """Get SQL query statistics."""
        if not self.query_context:
            return {
                "query_count": 0,
                "total_time": 0.0,
                "avg_time": 0.0,
                "silk_enabled": self.enabled,
                "capture_queries_context": False,
            }

        queries = self.query_context.captured_queries
        query_count = len(queries)
        total_time = sum(float(q.get("time", 0)) for q in queries)
        avg_time = total_time / query_count if query_count > 0 else 0.0

        return {
            "query_count": query_count,
            "total_time": total_time,
            "avg_time": avg_time,
            "silk_enabled": self.enabled,
            "capture_queries_context": True,
        }
