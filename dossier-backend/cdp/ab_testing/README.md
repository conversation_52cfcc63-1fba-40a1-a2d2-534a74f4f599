# CDP A/B Testing Framework

This framework provides tools for comparing different implementations of the CDP recommendation services.

## Overview

The CDP module has three different implementations for recommendation services:

1. **Legacy approach**: The pre-refactored implementation
2. **Top-down approach**: Starts with documents and finds relevant page objects
3. **Bottom-up approach**: Starts with page objects and optimizes queries

This framework allows you to:

- Compare the performance of these approaches
- Track the number of SQL queries used by each approach
- Verify that they produce the same results or analyze differences
- Test with different dossiers, field definitions, and field sets
- Perform focused consistency checks on specific field definitions
- Run batch analyses across multiple dossiers and field definitions
- Generate reports in JSON or console format for analysis

## Components

- **framework.py**: Core A/B testing and analysis framework (`ABTestingFramework`).
- **utils.py**: Utility functions for parsing and encoding.
- **silk_utils.py**: Utilities for Django Silk integration.
- **cdp/management/commands/run_cdp_tests.py**: The unified Django management command for all tests.
- **api.py**: API endpoints for A/B testing (primarily for interactive or programmatic API-based testing).

## SQL Query Tracking

The framework integrates with Django Silk to track the number of SQL queries used by each approach. To enable SQL query tracking:

1. Set `DEBUG_ENABLE_SILK=True` in your `.env` file
2. Make sure you've run the Silk migrations: `python manage.py migrate silk`
3. Run your A/B tests

The framework will automatically track and report the number of SQL queries used by each approach.

## Usage

### Unified Management Command: `run_cdp_tests`

The primary way to run A/B tests and consistency analyses is through the `run_cdp_tests` Django management command. This command consolidates the functionality of previous separate scripts and commands.

**Base Command:**
```bash
python manage.py run_cdp_tests <test_type> [common_options] [test_type_specific_options]
```

**Test Types (`<test_type>`):**

*   `grouped_comparison`: Compares grouped recommendations for specified approaches, dossiers, field sets, and field definitions.
*   `num_recs_comparison`: Compares the number of recommendations (total, hints, grouped) for specified approaches, dossiers, field sets, and field definitions.
*   `consistency_check`: Performs repeated `num_recs` tests on a single field definition for a single dossier to check for inconsistencies across runs and approaches.
*   `batch_num_recs_analysis`: Runs a comprehensive `num_recs` analysis across multiple FDs (all in a field set or specified) over multiple dossiers, aggregating results and analyzing consistency.
*   `batch_grouped_analysis`: Runs a comprehensive analysis for full grouped recommendations across multiple FDs (all in a field set or specified) over multiple dossiers, aggregating results, performance, and diffs.

**Common Options:**

*   `--dossier-uuids <UUIDs_or_filepath>`: Comma-separated dossier UUIDs or path to a file with UUIDs (one per line).
*   `--num-dossiers <count>`: Number of random dossiers if `--dossier-uuids` isn't used (default: `1`).
*   `--field-set-keys <keys>`: Comma-separated field set keys (default: `hd_internal`).
*   `--approaches <approaches>`: Comma-separated approaches (default: `legacy,top_down,bottom_up`).
*   `--output-format <format>`: Output format (`console`, `json_file`). Default: `console`.
*   `--output-file <path>`: Path for `json_file` output. A default name is generated if not provided.
*   `--verbose`: Enable verbose logging.
*   `--testrun-name <name>`: Optional name for the test run, used in JSON output and default filenames.

**Test-Type Specific Options:**

*   **`grouped_comparison`**:
    *   `--field-definition-keys <keys>`: Comma-separated FD keys. If one, tests that. If multiple, iterates. (Required if not derivable from field sets)
*   **`num_recs_comparison`**:
    *   `--field-definition-keys <keys>`: Comma-separated FD keys. If none, all FDs in specified field sets are used.
*   **`consistency_check`**:
    *   `--field-definition-key <key>`: Single FD key (Required).
    *   `--num-runs <count>`: Number of repeated runs (default: `3`).
*   **`batch_num_recs_analysis`**:
    *   `--field-definition-keys <keys>`: Optional comma-separated FD keys. If none, all FDs in specified field sets are used.
*   **`batch_grouped_analysis`**:
    *   `--field-definition-keys <keys>`: Optional comma-separated FD keys. If none, all FDs in specified field sets are used.

**Example Usages:**

*   Show help for the main command and see subparsers:
    ```bash
    python manage.py run_cdp_tests --help
    ```
*   Show help for a specific test type, e.g., `consistency_check`:
    ```bash
    python manage.py run_cdp_tests consistency_check --help
    ```
*   Run a `grouped_comparison` for 2 random dossiers, default field set, all FDs, verbose output:
    ```bash
    python manage.py run_cdp_tests grouped_comparison --num-dossiers 2 --verbose
    ```
*   Run a `num_recs_comparison` for specific dossier UUIDs and FD keys, outputting to a named JSON file:
    ```bash
    python manage.py run_cdp_tests num_recs_comparison --dossier-uuids "uuid1,uuid2" --field-definition-keys "FD_KEY_1,FD_KEY_2" --output-format json_file --output-file "results/my_num_recs_test.json" --testrun-name "MyNumRecsRun1"
    ```
*   Run a `consistency_check` for a specific dossier and FD, 5 runs:
    ```bash
    python manage.py run_cdp_tests consistency_check --dossier-uuids "your-dossier-uuid" --field-definition-key "FD_DOSSIER_HEADLINE" --num-runs 5
    ```
*   Run a `batch_num_recs_analysis` for 10 dossiers, `hd_internal` fieldset, all its FDs, comparing `top_down` and `bottom_up`:
    ```bash
    python manage.py run_cdp_tests batch_num_recs_analysis --num-dossiers 10 --field-set-keys "hd_internal" --approaches "top_down,bottom_up" --output-format json_file
    ```
*   Run a `batch_grouped_analysis` for 5 dossiers, `hd_internal` fieldset, all its FDs, comparing all approaches, output to JSON:
    ```bash
    python manage.py run_cdp_tests batch_grouped_analysis --num-dossiers 5 --field-set-keys "hd_internal" --output-format json_file --testrun-name "BatchGroupedInternalAllFDs"
    ```

### API Endpoints

The framework also exposes API endpoints for more direct programmatic or interactive testing:

- `/cdp-api/ab_testing/grouped_recommendations/{approach}/`: Get recommendations using a specific approach.
- `/cdp-api/ab_testing/num_recommendations/{approach}/`: Get number of recommendations using a specific approach.
- `/cdp-api/ab_testing/compare_approaches/`: Compare different approaches for a grouped recommendation request.
- `/cdp-api/ab_testing/compare_num_recommendations/`: Compare different approaches for a num_recommendations request.

These are useful for integration with other tools or for quick checks via an API client.

### Programmatic Usage of the Framework

You can use the `ABTestingFramework` programmatically in your own scripts or Django extensions if needed, though the `run_cdp_tests` command should cover most use cases.

```python
from cdp.ab_testing.framework import ABTestingFramework
from uuid import uuid4 # For example

# Initialize the framework (ensure Django is setup if running outside manage.py)
# from cdp.api import field_context_service # If needed for constructor
# framework = ABTestingFramework(field_context_service=field_context_service)
framework = ABTestingFramework()


# Example: Compare grouped approaches
dossier_uuid_example = uuid4() # Replace with actual UUID
field_set_key_example = "hd_internal"
fd_key_example = "FD_DOSSIER_HEADLINE" # Replace with actual FD

# Ensure dossier_uuid_example, fd_key_example, field_set_key_example are valid and exist
# For a real run, you would fetch these from your DB or have them predefined.

# grouped_comparison_result = framework.compare_approaches(
# dossier_uuid=dossier_uuid_example,
# field_set_key=field_set_key_example,
# field_definition_key=fd_key_example,
# approaches=["legacy", "top_down"]
# )
# print(grouped_comparison_result.get_summary())

# Example: Perform a consistency check
# consistency_result = framework.perform_consistency_check(
# dossier_uuid=dossier_uuid_example,
# field_set_key=field_set_key_example,
# field_definition_key=fd_key_example,
# approaches=["top_down", "bottom_up"],
# num_runs=3
# )
# print(json.dumps(consistency_result, indent=2, cls=ABTestJSONEncoder)) # If you have ABTestJSONEncoder

# Example: Perform batch num_recs analysis
# batch_num_recs_result = framework.perform_batch_num_recs_analysis(
# dossier_uuids=[dossier_uuid_example, another_dossier_uuid], # List of UUIDs
# field_set_key=field_set_key_example,
# approaches=["legacy", "top_down"],
#     # field_definition_keys=["FD_1", "FD_2"] # Optional, else all in field set
# )
# print(json.dumps(batch_num_recs_result, indent=2, cls=ABTestJSONEncoder))

# Example: Perform batch grouped analysis
# batch_grouped_result = framework.perform_batch_grouped_analysis(
# dossier_uuids=[dossier_uuid_example, another_dossier_uuid],
# field_set_key=field_set_key_example,
# approaches=["legacy", "top_down"],
#     # field_definition_keys=["FD_1", "FD_2"] # Optional, else all in field set
# )
# print(json.dumps(batch_grouped_result, indent=2, cls=ABTestJSONEncoder))

The `ABTestingBatch` class previously used for batch `grouped_recommendations` has been effectively superseded by looping within the `grouped_comparison` handler in the `run_cdp_tests` command or by using `perform_batch_num_recs_analysis` for num_recs type batch tests or `perform_batch_grouped_analysis` for full grouped recommendation batch tests. Direct use of `ABTestingBatch` is generally no longer the primary recommended path.
