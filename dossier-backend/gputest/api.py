from datetime import timed<PERSON><PERSON>

import structlog
from django.conf import settings
from django.db import transaction
from django.http import Http404
from django.shortcuts import get_object_or_404
from django.utils import timezone
from ninja import NinjaAPI, UploadedFile, File, Form
from ninja.errors import ValidationError
from ninja.security import Http<PERSON>earer

from core.publisher import publish
from core.schema import Message, Error
from dossier import models as dossier_models
from dossier import schemas as dossier_schema
from dossier import services as dossier_services
from dossier import services_external as dossier_services_external
from dossier.helpers_api import handle_api_validation_error

from projectconfig.authentication import (
    authenticate_from_account,
)
from projectconfig.settings import (
    ASYNC_DOSSIER_SEMANTIC_DOCUMENT_PDF_WORKER_ROUTING_KEY,
)

from semantic_document.services import (
    export_semantic_document_wrapper_with_access_check,
)

from workers.models import SemanticDocumentExport
from gputest.schemas.schemas import (
    SemanticDocumentPDFExportRequest,
)
import gputest.schemas.schemas as gputest_schemas

from gputest.services import gputest_get_dossier_with_access_check

logger = structlog.get_logger()


class gputestJWTAuth(HttpBearer):
    def authenticate(self, request, token, *args, **kw):
        jwt = authenticate_from_account(token)
        if settings.API_ROLE not in jwt.user_roles:
            return
        return jwt


api = NinjaAPI(
    title="Hypodossier - gputest API",
    csrf=False,
    auth=gputestJWTAuth(),
    urls_namespace="gputest-api",
    version="0.1.0",
    servers=[],
)


@api.exception_handler(ValidationError)
def custom_validation_errors(request, exc):
    return handle_api_validation_error(api, logger, exc, request)


def map_dossier(dossier):
    # Same as SWISSFEX, but keep duplicate code, as dossier schema might diverge
    return gputest_schemas.Dossier(
        uuid=dossier.uuid,
        external_dossier_id=str(dossier.external_id),
        updated_at=dossier.updated_at,
        created_at=dossier.created_at,
    )


@api.get("/ping", response={200: Message}, url_name="ping", exclude_none=True)
def ping(request):
    return Message(detail="pong")


@api.post(
    "/dossier",
    response={201: gputest_schemas.Dossier, 409: Error},
    url_name="create-dossier",
    exclude_none=True,
)
def create_dossier(request, dossier_create: gputest_schemas.CreateDossier):
    dossier_user = request.auth.get_user_or_create()
    account = dossier_user.account

    # Check if dossier already exists, and return a 409 conflict if it does
    dossier = dossier_models.Dossier.objects.filter(
        account=account, external_id=dossier_create.external_dossier_id
    ).first()
    if dossier is not None:
        return 409, {
            "code": 409,
            "message": f"Dossier with external_dossier_id '{dossier_create.external_dossier_id}' already exists",
        }

    new_dossier = dossier_services.create_dossier(
        account=account,
        dossier_name=dossier_create.name,
        language=dossier_create.lang,
        owner=dossier_user.user,
        external_id=dossier_create.external_dossier_id,
    )
    new_dossier.save()

    return 201, {
        "external_dossier_id": new_dossier.external_id,
        "uuid": new_dossier.uuid,
        "updated_at": new_dossier.updated_at,
        "created_at": new_dossier.created_at,
    }


@api.patch(
    "/dossier/{external_dossier_id}",
    response={201: gputest_schemas.Dossier, 409: Error, 404: Message},
    url_name="update-dossier",
    exclude_none=True,
)
def update_dossier(
    request, external_dossier_id: str, dossier_change: gputest_schemas.ChangeDossier
):
    """Updates a new Dossier based on the provided parameters

    We provide a external_dossier_id as part of the URL and allow the client to change it
    as part of ChangeDossier
    """

    dossier_user = request.auth.get_user_or_create()

    dossier = gputest_get_dossier_with_access_check(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    for attr, value in dossier_change.model_dump().items():
        if value:
            setattr(dossier, attr, value)

    dossier.save()
    dossier.refresh_from_db()

    return 201, {
        "external_dossier_id": dossier.external_id,
        "uuid": dossier.uuid,
        "updated_at": dossier.updated_at,
        "created_at": dossier.created_at,
    }


@api.post(
    "/dossier/{external_dossier_id}/original-files",
    response={201: dossier_schema.CreatedObjectReference, 409: Message, 404: Message},
    url_name="add-original-file",
    exclude_none=True,
)
@transaction.atomic
def add_original_file(
    request,
    external_dossier_id: str,
    file: UploadedFile = File(...),
    allow_duplicate_and_rename: bool = Form(False),
):
    """Add an original file to a dossier"""
    dossier_user = request.auth.get_user_or_create()

    dossier = gputest_get_dossier_with_access_check(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    response_code, original_file = dossier_services_external.add_original_file(
        dossier=dossier,
        file=file,
        allow_duplicate_and_rename=allow_duplicate_and_rename,
    )

    return response_code, original_file


@api.post(
    "/export/dossier/{external_dossier_id}/semantic-documents/{semantic_document_uuid}",
    response={200: gputest_schemas.SemanticDocumentPDFExportRequest, 404: Message},
    url_name="export-dossier-semantic-document-pdf",
)
def export_semantic_document_pdf(
    request,
    external_dossier_id,
    semantic_document_uuid,
):
    request = export_semantic_document_wrapper_with_access_check(
        dossier_user=request.auth.get_user_or_create(),
        external_dossier_id=external_dossier_id,
        semantic_document_uuid=semantic_document_uuid,
    )

    publish(
        message=request.model_dump_json().encode(),
        routing_key=ASYNC_DOSSIER_SEMANTIC_DOCUMENT_PDF_WORKER_ROUTING_KEY,
    )

    return SemanticDocumentPDFExportRequest(
        uuid=request.semantic_document_pdf_request_uuid,
    )


@api.get(
    "/export/{semantic_document_export_request_uuid}/status",
    response=gputest_schemas.ExportStatus,
    url_name="dossier-semantic-document-export-status",
    exclude_none=True,
)
def get_available_export(request, semantic_document_export_request_uuid: str):
    # Check the status of an individual document

    dossier_user = request.auth.get_user_or_create()
    account = dossier_user.account

    export_semantic_document: SemanticDocumentExport = get_object_or_404(
        SemanticDocumentExport, uuid=semantic_document_export_request_uuid
    )

    # Check that user has access to an associated account
    if export_semantic_document.semantic_document.dossier.account != account:
        raise Http404("No permission to access this dossier")

    status = gputest_schemas.ExportProcessingStatus.PROCESSING
    dossier_url = None
    dossier_file_uuid = None
    if export_semantic_document.done:
        status = gputest_schemas.ExportProcessingStatus.PROCESSED
        dossier_url = export_semantic_document.file.get_fast_url()
        dossier_file_uuid = export_semantic_document.file.uuid

    return gputest_schemas.ExportStatus(
        semantic_document_export_request_uuid=semantic_document_export_request_uuid,
        status=status,
        dossier_url=dossier_url,
        dossier_file_uuid=dossier_file_uuid,
        updated_at=export_semantic_document.done,
    )


@api.delete(
    "/dossier/{external_dossier_id}",
    response={202: Message, 404: Message},
    url_name="dossier-delete",
    exclude_none=True,
)
def delete_dossier(request, external_dossier_id):
    dossier_user = request.auth.get_user_or_create()

    dossier = gputest_get_dossier_with_access_check(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    dossier.expiry_date = timezone.now() - timedelta(days=1)
    dossier.save()

    return 202, {"detail": "Dossier Scheduled for deletion"}
