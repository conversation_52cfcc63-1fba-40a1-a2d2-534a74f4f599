import uuid
import pytest

from django.urls import reverse
from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>

from dossier.doc_cat_helpers import UNKNOWN_DOCUMENT_CATEGORY_KEYS
from dossier.fakes import add_some_fake_semantic_documents
from dossier.models import (
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>r<PERSON><PERSON>,
    Account,
    DossierAccessGrant,
    DocumentCategory,
    DossierCloseStrategy,
)
from dossier.schemas import Language
from dossier.services import create_dossier
from dossier.tests.common_state import assert_dossier_access_mode
from dossier.tests.test_utils import mock_publish_side_effect_services_rabbit_mq_publish

from finnova import schemas
from finnova.models import FinnovaDossierProperties
from django.utils import timezone

from finnova.schemas import (
    AccountName,
    Scope,
    DossierCloseReadyResponse,
    DossierCloseResponse,
)
from projectconfig.authentication import get_user_or_create
from workers.models import SemanticDocumentExport

pytestmark = pytest.mark.django_db


def _create_dossier():
    external_dossier_id = "Mandant.Kundennummer.Rahmennummer.Antragsnummer-" + str(
        uuid.uuid4()
    )

    account = Account.objects.get(key=AccountName.finnovadev.value)

    dossier_user: DossierUser = get_user_or_create(
        account=account,
        username="<EMAIL>",
    )

    new_dossier = create_dossier(
        account=account,
        dossier_name="Test Dossier",
        language="de",
        owner=dossier_user.user,
        external_id=external_dossier_id,
    )
    new_dossier.save()

    return external_dossier_id, new_dossier, account, dossier_user


def test_show_dossier(
    finnova_authenticated_client,
    finnova_account,
    set_finnova_JWK,
):

    external_dossier_id, new_dossier, account, dossier_user = _create_dossier()

    assert (
        finnova_authenticated_client.get(
            path=reverse(
                "finnova-api:show-dossier",
                kwargs={"external_dossier_id": external_dossier_id},
            ),
        ).status_code
        == 302
    )

    assert (
        finnova_authenticated_client.get(
            path=reverse(
                "finnova-api:show-dossier",
                kwargs={"external_dossier_id": "doesnt-exist"},
            ),
        ).status_code
        == 404
    )


def test_update_dossier_api_success(
    finnova_authenticated_client,
    finnova_account,
    set_finnova_JWK,
):
    """
    1. Create "Test Dossier" with lang de
    2. Update name to "Test Dossier 2" and change lang to fr
    3. Update name to "Test Dossier 3" and do not change lang
    @param finnova_authenticated_client:
    @param finnova_account:
    @param set_finnova_JWK:
    @return:
    """

    external_dossier_id, new_dossier, account, dossier_user = _create_dossier()

    # Test updating multiple fields
    result = finnova_authenticated_client.patch(
        path=reverse(
            "finnova-api:update-dossier",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
        data=schemas.ChangeDossier(
            name="Test Dossier 2",
            lang=Language.fr,
            sequence_number="12345",
            financing_number="FIN-001",
            client_id="CLIENT-001",
            client_key="KEY-001",
        ).model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 200
    response_data = result.json()

    assert response_data["external_dossier_id"] == external_dossier_id
    assert response_data["name"] == "Test Dossier 2"
    assert response_data["lang"] == "fr"
    assert response_data["sequence_number"] == "12345"
    assert response_data["financing_number"] == "FIN-001"
    assert response_data["client_id"] == "CLIENT-001"
    assert response_data["client_key"] == "KEY-001"

    # Verify database changes
    dossier = Dossier.objects.get(external_id=external_dossier_id)
    assert dossier.name == "Test Dossier 2"
    assert dossier.lang == "fr"

    finnova_properties = FinnovaDossierProperties.objects.get(dossier=dossier)
    assert finnova_properties.sequence_number == "12345"
    assert finnova_properties.financing_number == "FIN-001"
    assert finnova_properties.client_id == "CLIENT-001"
    assert finnova_properties.client_key == "KEY-001"

    # Test partial update
    result = finnova_authenticated_client.patch(
        path=reverse(
            "finnova-api:update-dossier",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
        data=schemas.ChangeDossier(
            name="Test Dossier 3",
            financing_number="FIN-002",
        ).model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 200
    response_data = result.json()

    assert response_data["external_dossier_id"] == external_dossier_id
    assert response_data["name"] == "Test Dossier 3"
    assert response_data["lang"] == "fr"  # Unchanged
    assert response_data["sequence_number"] == "12345"  # Unchanged
    assert response_data["financing_number"] == "FIN-002"
    assert response_data["client_id"] == "CLIENT-001"  # Unchanged
    assert response_data["client_key"] == "KEY-001"  # Unchanged

    # Verify database changes after partial update
    dossier.refresh_from_db()
    finnova_properties.refresh_from_db()

    assert dossier.name == "Test Dossier 3"
    assert dossier.lang == "fr"
    assert finnova_properties.sequence_number == "12345"
    assert finnova_properties.financing_number == "FIN-002"
    assert finnova_properties.client_id == "CLIENT-001"
    assert finnova_properties.client_key == "KEY-001"


def test_delete_dossier_api_success(
    finnova_authenticated_client,
    finnova_account,
    set_finnova_JWK,
):
    external_dossier_id, new_dossier, account, dossier_user = _create_dossier()

    assert Dossier.objects.filter(external_id=external_dossier_id).exists()

    # Test updating multiple fields
    result = finnova_authenticated_client.delete(
        path=reverse(
            "finnova-api:dossier-delete",
            kwargs={"external_dossier_id": external_dossier_id},
        )
    )

    assert result.status_code == 202

    assert Dossier.objects.filter(external_id=external_dossier_id).exists() is False


@pytest.fixture
def create_access_grant_data():
    def _create_data(external_dossier_id, expires_at=None):
        return schemas.DossierAccessGrant(
            name="Test Dossier",
            external_dossier_id=external_dossier_id,
            username="<EMAIL>",
            email="<EMAIL>",
            first_name="Test",
            last_name="User",
            expires_at=expires_at or (timezone.now() + timezone.timedelta(days=1)),
            scope=Scope.READ_ONLY.value,
        )

    return _create_data


def set_dossier_user_grant(client, data):
    return client.post(
        path=reverse("finnova-api:set-dossier-user-grant"),
        data=data.model_dump_json(),
        content_type="application/json",
    )


@pytest.mark.parametrize(
    "has_access,expected_message",
    [
        (True, "Access <NAME_EMAIL>"),
        (False, "Access expired"),
    ],
)
def test_set_dossier_user_grant(
    set_finnova_JWK,
    finnova_authenticated_client,
    finnova_account,
    create_access_grant_data,
    has_access,
    expected_message,
):
    external_dossier_id, dossier, account, dossier_user = _create_dossier()

    if has_access:
        data = create_access_grant_data(
            external_dossier_id,
            expires_at=(timezone.now() + timezone.timedelta(days=1)),
        )
    else:
        data = create_access_grant_data(
            external_dossier_id,
            expires_at=(timezone.now() - timezone.timedelta(days=1)),
        )

    response = set_dossier_user_grant(finnova_authenticated_client, data)

    assert response.status_code == 200
    assert expected_message in response.json()["detail"]

    if has_access:
        assert (
            DossierAccessGrant.objects.filter(
                dossier=dossier,
                user__username="<EMAIL>",
                expires_at__gt=timezone.now(),
            )
            .first()
            .has_access
        )
    else:
        assert (
            DossierAccessGrant.objects.filter(
                dossier=dossier,
                user__username="<EMAIL>",
                expires_at__gt=timezone.now(),
            ).first()
            is None
        )


def test_set_dossier_user_grant_expire_and_revoke(
    set_finnova_JWK,
    finnova_authenticated_client,
    finnova_account,
    create_access_grant_data,
):
    external_dossier_id, dossier, account, dossier_user = _create_dossier()
    data = create_access_grant_data(external_dossier_id)

    # Grant access
    response = set_dossier_user_grant(finnova_authenticated_client, data)
    assert response.status_code == 200
    assert (
        "Access <NAME_EMAIL>" in response.json()["detail"]
    )
    assert (
        DossierAccessGrant.objects.filter(
            dossier=dossier,
            user__username="<EMAIL>",
            expires_at__gt=timezone.now(),
        )
        .first()
        .has_access
    )

    # Expire access
    expired_date = timezone.now() - timezone.timedelta(days=1)
    data.expires_at = expired_date
    response = set_dossier_user_grant(finnova_authenticated_client, data)
    assert response.status_code == 200
    assert "Access expired" in response.json()["detail"]
    assert (
        DossierAccessGrant.objects.filter(
            dossier=dossier,
            user__username="<EMAIL>",
            expires_at__gt=timezone.now(),
        ).first()
        is None
    )


def test_set_dossier_user_grant_dossier_not_found(
    set_finnova_JWK,
    finnova_authenticated_client,
    finnova_account,
    create_access_grant_data,
):
    data = create_access_grant_data(
        "Mandant.Kundennummer.Rahmennummer.Antragsnummer-" + str(uuid.uuid4())
    )
    response = set_dossier_user_grant(finnova_authenticated_client, data)
    assert response.status_code == 404


def test_set_dossier_user_grant_update_existing(
    set_finnova_JWK,
    finnova_authenticated_client,
    finnova_account,
    create_access_grant_data,
):
    external_dossier_id, dossier, account, dossier_user = _create_dossier()
    data = create_access_grant_data(external_dossier_id)

    # Grant access
    response = set_dossier_user_grant(finnova_authenticated_client, data)
    assert response.status_code == 200
    assert (
        "Access <NAME_EMAIL>" in response.json()["detail"]
    )
    assert (
        DossierAccessGrant.objects.filter(
            dossier=dossier,
            user__username="<EMAIL>",
            expires_at__gt=timezone.now(),
        )
        .first()
        .has_access
    )

    # Update access
    new_date = timezone.now() + timezone.timedelta(days=2)
    data.expires_at = new_date
    response = set_dossier_user_grant(finnova_authenticated_client, data)
    assert response.status_code == 200
    assert (
        "Access <NAME_EMAIL>" in response.json()["detail"]
    )
    assert (
        DossierAccessGrant.objects.filter(
            dossier=dossier,
            user__username="<EMAIL>",
            expires_at__gt=timezone.now(),
        )
        .first()
        .has_access
    )


def remove_msg_nok_for_testing(rr: DossierCloseReadyResponse):
    """
    For easier comparison we do not want to check the error messages every time
    @param rr:
    @return:
    """
    rr2 = DossierCloseReadyResponse(**rr.model_dump())

    if not rr2.ready_for_close:
        rr2.msg_nok_de = None
        rr2.msg_nok_en = None
        rr2.msg_nok_fr = None
        rr2.msg_nok_it = None
    return rr2


@pytest.mark.parametrize(
    "allow_unknown_documents,strategy,expected_ready_dossier_close1,expected_ready_dossier_close2",
    [
        # This strategy allows everything :-)
        (False, DossierCloseStrategy.DEFAULT, True, True),
        # Second call cannot archive the unknown document in the close
        (
            False,
            DossierCloseStrategy.EXPORT_SEMANTIC_DOCUMENTS_IN_DOSSIER_CLOSE,
            True,
            False,
        ),
        # Second call CAN archive the unknown document in the close
        (
            True,
            DossierCloseStrategy.EXPORT_SEMANTIC_DOCUMENTS_IN_DOSSIER_CLOSE,
            True,
            True,
        ),
        # One is pending so do not allow first export, same for second
        (
            True,
            DossierCloseStrategy.REQUIRE_SEMANTIC_DOCUMENT_EXPORT_DONE,
            False,
            False,
        ),
    ],
)
def test_check_dossier_close_ready(
    set_finnova_JWK,
    finnova_authenticated_client,
    finnova_account,
    document_categories,
    create_access_grant_data,
    allow_unknown_documents: bool,
    strategy: DossierCloseStrategy,
    expected_ready_dossier_close1: bool,
    expected_ready_dossier_close2: bool,
):

    finnova_account.enable_semantic_document_export_unknown_documents = (
        allow_unknown_documents
    )
    finnova_account.dossier_close_strategy = strategy
    finnova_account.save()

    external_dossier_id, dossier, account, dossier_user = _create_dossier()

    valid_document_categories = list(
        DocumentCategory.objects.filter(account=account)
        .exclude(name__in=UNKNOWN_DOCUMENT_CATEGORY_KEYS)
        .values_list("name", flat=True)
    )

    # Have 1 semantic document that's in the process of being exported
    semantic_document_exported_unfinished = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=valid_document_categories,
        max_pages=5,
        min_num_pages=5,
    )[0]

    SemanticDocumentExport.objects.create(
        semantic_document=semantic_document_exported_unfinished
    )

    # Have 5 semantic documents that have already been exported (we want to test the .distinct() in the query)
    semantic_documents_exported_done = []
    for i in range(5):
        semantic_document_exported_done = add_some_fake_semantic_documents(
            dossier,
            num_docs=1,
            allow_empty_docs=False,
            valid_document_category_keys=valid_document_categories,
            max_pages=5,
            min_num_pages=5,
        )[0]

        SemanticDocumentExport.objects.create(
            semantic_document=semantic_document_exported_done, done=timezone.now()
        )

        semantic_documents_exported_done.append(semantic_document_exported_done)

    # Add 2 document that are not exported yet
    for i in range(2):
        add_some_fake_semantic_documents(
            dossier,
            num_docs=1,
            allow_empty_docs=False,
            valid_document_category_keys=valid_document_categories,
            max_pages=5,
            min_num_pages=5,
        )

    close_ready_response1 = DossierCloseReadyResponse.model_validate_json(
        finnova_authenticated_client.get(
            path=reverse(
                "finnova-api:check-dossier-close-ready",
                kwargs={"external_dossier_id": external_dossier_id},
            ),
        ).content
    )

    expected_response1 = DossierCloseReadyResponse(
        num_documents_all=5 + 2 + 1,
        num_documents_exported=5,
        num_documents_export_not_started=2,
        num_documents_in_export=1,
        num_documents_unknown=0,
        num_original_files_in_processing=0,
        ready_for_close=expected_ready_dossier_close1,
    )
    assert remove_msg_nok_for_testing(close_ready_response1) == expected_response1
    if expected_ready_dossier_close1:
        assert close_ready_response1.msg_nok_de is None
    else:
        assert close_ready_response1.msg_nok_de is not None

    # Add another semantic document that's in the unknown category, so its not ready for export
    add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=UNKNOWN_DOCUMENT_CATEGORY_KEYS,
        max_pages=5,
        min_num_pages=5,
    )

    close_ready_response2 = DossierCloseReadyResponse.model_validate_json(
        finnova_authenticated_client.get(
            path=reverse(
                "finnova-api:check-dossier-close-ready",
                kwargs={"external_dossier_id": external_dossier_id},
            ),
        ).content
    )

    expected_response2 = DossierCloseReadyResponse(
        num_documents_all=5 + (2 + 1) + 1,
        num_documents_exported=5,
        num_documents_export_not_started=2 + 1,
        num_documents_in_export=1,
        num_documents_unknown=1,
        num_original_files_in_processing=0,
        ready_for_close=expected_ready_dossier_close2,
    )
    assert remove_msg_nok_for_testing(close_ready_response2) == expected_response2
    if expected_ready_dossier_close2:
        assert close_ready_response2.msg_nok_de is None
    else:
        assert close_ready_response2.msg_nok_de is not None


def test_close_dossier(
    set_finnova_JWK,
    finnova_authenticated_client,
    finnova_account,
    document_categories,
    create_access_grant_data,
    mocker: MockerFixture,
):
    external_dossier_id, dossier, account, dossier_user = _create_dossier()

    # Add unknown document
    semantic_document = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=UNKNOWN_DOCUMENT_CATEGORY_KEYS,
        max_pages=2,
        min_num_pages=2,
    )[0]

    mocker.patch(
        "semantic_document.services.rabbit_mq_publish",
        side_effect=mock_publish_side_effect_services_rabbit_mq_publish,
    )

    # Step 1: check if dossier can be closed with 1 unknown document not exported
    close_ready_response1: DossierCloseResponse = (
        DossierCloseResponse.model_validate_json(
            finnova_authenticated_client.get(
                path=reverse(
                    "finnova-api:close-dossier",
                    kwargs={"external_dossier_id": external_dossier_id},
                ),
            ).content
        )
    )

    assert close_ready_response1.success is False
    assert close_ready_response1.msg_nok_de

    # Step 2: check if dossier can be closed after 1 document not exported was renamed

    semantic_document.document_category = DocumentCategory.objects.filter(
        account=account, name="TAX_DECLARATION"
    ).first()
    semantic_document.save()

    d1 = Dossier.objects.get(uuid=dossier.uuid)
    assert d1.access_mode == Dossier.DossierAccessMode.READ_WRITE
    old_expiry_date = d1.expiry_date
    close_ready_response2: DossierCloseResponse = (
        DossierCloseResponse.model_validate_json(
            finnova_authenticated_client.get(
                path=reverse(
                    "finnova-api:close-dossier",
                    kwargs={"external_dossier_id": external_dossier_id},
                ),
            ).content
        )
    )

    assert close_ready_response2.success
    assert close_ready_response2.msg_nok_de is None

    d2 = Dossier.objects.get(uuid=dossier.uuid)

    assert_dossier_access_mode(d2, Dossier.DossierAccessMode.READ_ONLY)
    assert d2.expiry_date < old_expiry_date

    # # Try to close the dossier again
    # close_ready_response3 = finnova_authenticated_client.get(
    #     path=reverse(
    #         "finnova-api:close-dossier",
    #         kwargs={"external_dossier_id": external_dossier_id},
    #     ),
    # )

    # # Failure as dossier soft deleted
    # assert close_ready_response3.status_code == 404
    # assert "does not exist" in Message.model_validate_json(close_ready_response3.content).detail
