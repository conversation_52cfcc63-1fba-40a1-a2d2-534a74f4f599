from typing import List


import structlog
from pydantic import BaseModel


from dossier.helpers_access_check import get_dossier_with_access_check
from dossier.exceptions import HttpError

import dossier.models as dossier_models

logger = structlog.get_logger(__name__)


class ContractException(BaseModel):
    _class: str


class Msg(BaseModel):
    key: str
    message: str
    args: List[str] = []


class ServiceException(ContractException):
    msg: Msg


class PropertyFailure(BaseModel):
    path: str
    value: str
    proposal: str = None


class ValidationFailure(BaseModel):
    msg: Msg
    properties: List[PropertyFailure]


class ValidationException(ServiceException):
    failures: List[ValidationFailure]


def pentest_get_dossier_with_access_check(
    dossier_user, external_dossier_id: str, is_manager=True
):
    """Custom wrapper for get_dossier_with_access_check to return a 404 with custom message if the dossier does not
    exist
    """
    # A little bit dirty as we are making two calls to the DB, but in terms of performance
    # should be fine
    if (
        dossier_models.Dossier.objects.filter(external_id=external_dossier_id).exists()
        is False
    ):
        raise HttpError(
            status=404,
            detail=f"Dossier with external id '{external_dossier_id}' does not exist",
        )

    dossier = get_dossier_with_access_check(
        # dossier_user=request.auth.get_user_or_create(),
        dossier_user=dossier_user,
        is_manager=is_manager,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    return dossier
