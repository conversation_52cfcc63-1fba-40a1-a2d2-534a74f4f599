"""
Enhanced PDF metadata handling with prefixed keywords and XMP support.
Uses Pydantic 2 for clean data modeling and validation.
"""

import re
import uuid
import xml.etree.ElementTree as ET
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Dict, Any, Union, cast

import pypdf
import structlog
from pydantic import BaseModel, Field, ConfigDict, field_validator
from pypdf import PdfWriter
from pypdf.generic import (
    NameObject,
    DictionaryObject,  # Added for explicit type hinting
    StreamObject,  # Added for explicit type hinting for metadata_obj
    NumberObject,
    ArrayObject,
    ByteStringObject,
    TextStringObject,
    BooleanObject,  # Added for image interpolation fix
)

from dossier.doc_cat_helpers import (
    get_document_categories_by_name,
    get_document_categories_by_name_for_account,
)
from dossier.schemas import SemanticDocumentFullApiData
from semantic_document.models import SemanticDocument
from semantic_document.schemas import SemanticDocumentMetadata

logger = structlog.get_logger()

PDF_METADATA_DOCINFO_ENABLED = True

PDF_METADATA_DOCINFO_SCHEMA_VERSION = "1.0.0"

PDF_METADATA_DOCINFO_CREATOR = "Hypodossier AG"
PDF_METADATA_DOCINFO_PRODUCER = f"{PDF_METADATA_DOCINFO_CREATOR} Dossier Manager/Content v{PDF_METADATA_DOCINFO_SCHEMA_VERSION}"


class PrefixedKeyword(BaseModel):
    """Represents a prefixed keyword with namespace and value."""

    model_config = ConfigDict(frozen=True)

    prefix: str = Field(
        ..., description="Namespace prefix (e.g., 'category', 'suffix')"
    )
    value: str = Field(..., description="The actual value")

    def __str__(self) -> str:
        return f"{self.prefix}:{self.value}"

    @classmethod
    def from_string(cls, keyword_str: str) -> Optional["PrefixedKeyword"]:
        """Parse a prefixed keyword from string format."""
        if ":" not in keyword_str:
            return None
        prefix, value = keyword_str.split(":", 1)
        return cls(prefix=prefix.strip(), value=value.strip())


class ParsedKeywords(BaseModel):
    """Result of parsing keywords with structured and searchable terms separated."""

    model_config = ConfigDict(frozen=True)

    structured: Dict[str, str] = Field(
        default_factory=dict, description="Structured metadata by prefix"
    )
    searchable: List[str] = Field(
        default_factory=list, description="Human-readable search terms"
    )

    @classmethod
    def from_keywords_string(cls, keywords_string: str) -> "ParsedKeywords":
        """Parse keywords string into structured and searchable components."""
        if not keywords_string:
            return cls()

        keywords = [k.strip() for k in keywords_string.split(",") if k.strip()]
        structured = {}
        searchable = []

        for keyword in keywords:
            prefixed = PrefixedKeyword.from_string(keyword)
            if prefixed:
                structured[prefixed.prefix] = prefixed.value
            else:
                searchable.append(keyword)

        return cls(structured=structured, searchable=searchable)


class XMPMetadata(BaseModel):
    """XMP metadata structure for PDF documents."""

    model_config = ConfigDict(frozen=True)

    # Dublin Core namespace
    dc_title: Optional[str] = Field(None, alias="dc:title")
    dc_creator: Optional[str] = Field(None, alias="dc:creator")
    dc_subject: Optional[str] = Field(None, alias="dc:subject")
    dc_description: Optional[str] = Field(None, alias="dc:description")

    # PDF namespace
    pdf_keywords: Optional[str] = Field(None, alias="pdf:Keywords")
    pdf_producer: Optional[str] = Field(None, alias="pdf:Producer")

    # XMP namespace
    xmp_creator_tool: Optional[str] = Field(None, alias="xmp:CreatorTool")

    # XMP Media Management namespace - required for PDF/A-2b
    xmpMM_document_id: Optional[str] = Field(None, alias="xmpMM:DocumentID")
    xmpMM_instance_id: Optional[str] = Field(None, alias="xmpMM:InstanceID")

    # Custom HypoDossier namespace
    hypodossier_document_category_key: Optional[str] = Field(
        None, alias="hypodossier:DocumentCategoryKey"
    )
    hypodossier_title_suffix: Optional[str] = Field(
        None, alias="hypodossier:TitleSuffix"
    )
    hypodossier_version: Optional[str] = Field(None, alias="hypodossier:Version")
    hypodossier_uuid: Optional[str] = Field(None, alias="hypodossier:UUID")

    def to_xmp_xml(self) -> str:
        """Generate PDF/A-2b compliant XMP XML string."""

        # PDF/A-2 compliant XMP packet wrapper with XML declaration and BOM
        xmp_begin = '<?xml version="1.0" encoding="UTF-8"?>\n<?xpacket begin="\uFEFF" id="W5M0MpCehiHzreSzNTczkc9d"?>'
        xmp_end = '<?xpacket end="w"?>'

        # Create root element with proper namespace structure for PDF/A-2
        root = ET.Element(
            "x:xmpmeta",
            {
                "xmlns:x": "adobe:ns:meta/",
                "xmlns:rdf": "http://www.w3.org/1999/02/22-rdf-syntax-ns#",
            },
        )

        # All namespaces must be declared on rdf:RDF for PDF/A-2b compliance
        rdf = ET.SubElement(
            root,
            "rdf:RDF",
            {
                "xmlns:dc": "http://purl.org/dc/elements/1.1/",
                "xmlns:pdf": "http://ns.adobe.com/pdf/1.3/",
                "xmlns:xmp": "http://ns.adobe.com/xap/1.0/",
                "xmlns:pdfaid": "http://www.aiim.org/pdfa/ns/id/",
                "xmlns:xml": "http://www.w3.org/XML/1998/namespace",
                "xmlns:xmpMM": "http://ns.adobe.com/xap/1.0/mm/",
                "xmlns:hypodossier": "http://hypodossier.com/ns/1.0/",
            },
        )

        # PDF/A identification schema - required for PDF/A-2b compliance
        id_description = ET.SubElement(rdf, "rdf:Description", {"rdf:about": ""})
        ET.SubElement(id_description, "pdfaid:part").text = "2"
        ET.SubElement(id_description, "pdfaid:conformance").text = "B"

        # PDF/A-2 extension schema declaration for custom HypoDossier namespace
        ext_desc = ET.SubElement(
            rdf,
            "rdf:Description",
            {
                "rdf:about": "",
                "xmlns:pdfaExtension": "http://www.aiim.org/pdfa/ns/extension/",
                "xmlns:pdfaSchema": "http://www.aiim.org/pdfa/ns/schema#",
                "xmlns:pdfaProperty": "http://www.aiim.org/pdfa/ns/property#",
                "xmlns:pdfaType": "http://www.aiim.org/pdfa/ns/type#",
                "xmlns:pdfaField": "http://www.aiim.org/pdfa/ns/field#",
            },
        )
        schemas = ET.SubElement(ext_desc, "pdfaExtension:schemas")
        bag = ET.SubElement(schemas, "rdf:Bag")
        li_schema = ET.SubElement(bag, "rdf:li", {"rdf:parseType": "Resource"})

        # Identify the HypoDossier schema - add required namespaceURI for PDF/A-2b compliance
        ET.SubElement(li_schema, "pdfaSchema:namespaceURI").text = (
            "http://hypodossier.com/ns/1.0/"
        )
        ET.SubElement(li_schema, "pdfaSchema:schema").text = (
            "HypoDossier Custom Metadata Schema"
        )
        ET.SubElement(li_schema, "pdfaSchema:prefix").text = "hypodossier"

        # List each custom property's metadata
        prop_seq = ET.SubElement(
            ET.SubElement(li_schema, "pdfaSchema:property"), "rdf:Seq"
        )
        for name, desc in [
            ("DocumentCategoryKey", "Document category key"),
            ("TitleSuffix", "Optional title suffix"),
            ("Version", "HypoDossier schema version"),
            ("UUID", "Document UUID"),
        ]:
            prop_li = ET.SubElement(prop_seq, "rdf:li", {"rdf:parseType": "Resource"})
            ET.SubElement(prop_li, "pdfaProperty:name").text = name
            ET.SubElement(prop_li, "pdfaProperty:valueType").text = "Text"
            ET.SubElement(prop_li, "pdfaProperty:category").text = "external"
            ET.SubElement(prop_li, "pdfaProperty:description").text = desc

        # Main metadata description
        description = ET.SubElement(rdf, "rdf:Description", {"rdf:about": ""})

        # Get data with aliased field names
        data = self.model_dump(by_alias=True)

        # Dublin Core title - must be wrapped in rdf:Alt with xml:lang="x-default" for PDF/A-1
        if data.get("dc:title"):
            dc_title = ET.SubElement(description, "dc:title")
            alt = ET.SubElement(dc_title, "rdf:Alt")
            li = ET.SubElement(alt, "rdf:li", {"xml:lang": "x-default"})
            li.text = data["dc:title"]

        # Dublin Core creator - must be wrapped in rdf:Seq for PDF/A-1b compliance
        if data.get("dc:creator"):
            dc_creator = ET.SubElement(description, "dc:creator")
            seq = ET.SubElement(dc_creator, "rdf:Seq")
            li = ET.SubElement(seq, "rdf:li")
            li.text = data["dc:creator"]

        # Dublin Core description - must be wrapped in rdf:Alt with xml:lang="x-default" for PDF/A-1
        # This maps to /Subject in the Info dictionary
        if data.get("dc:description"):
            dc_description = ET.SubElement(description, "dc:description")
            alt = ET.SubElement(dc_description, "rdf:Alt")
            li = ET.SubElement(alt, "rdf:li", {"xml:lang": "x-default"})
            li.text = data["dc:description"]

        # Dublin Core format - mandatory for PDF/A-1b compliance
        ET.SubElement(description, "dc:format").text = "application/pdf"

        # PDF Keywords - simple element (maps to /Keywords in Info dictionary)
        if data.get("pdf:Keywords"):
            pdf_keywords = ET.SubElement(description, "pdf:Keywords")
            pdf_keywords.text = data["pdf:Keywords"]

        # PDF Producer - simple element (maps to /Producer in Info dictionary)
        if data.get("pdf:Producer"):
            pdf_producer = ET.SubElement(description, "pdf:Producer")
            pdf_producer.text = data["pdf:Producer"]

        # XMP CreatorTool - simple element (maps to /Creator in Info dictionary)
        if data.get("xmp:CreatorTool"):
            xmp_creator_tool = ET.SubElement(description, "xmp:CreatorTool")
            xmp_creator_tool.text = data["xmp:CreatorTool"]

        # XMP date stamps - required for PDF/A-2b compliance
        now = datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%SZ")
        ET.SubElement(description, "xmp:CreateDate").text = now
        ET.SubElement(description, "xmp:ModifyDate").text = now
        ET.SubElement(description, "xmp:MetadataDate").text = now

        # XMP Media Management fields - required for PDF/A-2b
        if data.get("xmpMM:DocumentID"):
            ET.SubElement(description, "xmpMM:DocumentID").text = data[
                "xmpMM:DocumentID"
            ]
        if data.get("xmpMM:InstanceID"):
            ET.SubElement(description, "xmpMM:InstanceID").text = data[
                "xmpMM:InstanceID"
            ]

        # HypoDossier custom fields - simple elements
        if data.get("hypodossier:DocumentCategoryKey"):
            hd_cat = ET.SubElement(description, "hypodossier:DocumentCategoryKey")
            hd_cat.text = data["hypodossier:DocumentCategoryKey"]

        if data.get("hypodossier:TitleSuffix"):
            hd_suffix = ET.SubElement(description, "hypodossier:TitleSuffix")
            hd_suffix.text = data["hypodossier:TitleSuffix"]

        if data.get("hypodossier:Version"):
            hd_version = ET.SubElement(description, "hypodossier:Version")
            hd_version.text = data["hypodossier:Version"]

        if data.get("hypodossier:UUID"):
            hd_uuid = ET.SubElement(description, "hypodossier:UUID")
            hd_uuid.text = data["hypodossier:UUID"]

        # Convert to string
        xml_str = ET.tostring(root, encoding="unicode", method="xml")
        return f"{xmp_begin}\n{xml_str}\n{xmp_end}"


class PdfMetadataDocinfo(BaseModel):
    """Enhanced PDF metadata model with Pydantic 2."""

    model_config = ConfigDict(frozen=True)

    version: str = Field(default=PDF_METADATA_DOCINFO_SCHEMA_VERSION)
    uuid: str = Field(..., description="Document UUID")
    title: str = Field(..., description="Document title")
    document_category_key: str = Field(..., description="Document category key")
    document_category_name: str = Field(
        ..., description="Human-readable category name, localized language"
    )
    document_category_title_de: Optional[str] = Field(
        ..., description="German category title"
    )
    document_category_title_en: Optional[str] = Field(
        ..., description="English category title"
    )
    document_category_title_fr: Optional[str] = Field(
        ..., description="French category title"
    )
    document_category_title_it: Optional[str] = Field(
        ..., description="Italian category title"
    )
    title_suffix: Optional[str] = Field(None, description="Optional title suffix")

    @field_validator("uuid")
    @classmethod
    def validate_uuid(cls, v: str) -> str:
        """Ensure UUID is a string."""
        return str(v)

    @field_validator("title_suffix")
    @classmethod
    def validate_title_suffix(cls, v: Optional[str]) -> Optional[str]:
        """Strip commas from title suffix to prevent keyword parsing issues."""
        if v is None:
            return v
        return v.replace(",", "")

    def get_full_title(self) -> str:
        """Get full title with suffix if available."""
        if self.title_suffix:
            return f"{self.title} {self.title_suffix}"
        return self.title

    def create_prefixed_keywords(self) -> List[str]:
        """Create prefixed keywords for structured metadata implementation."""
        keywords = [
            f"category:{self.document_category_key}",
            f"id:{self.uuid}",
        ]

        if self.title_suffix:
            keywords.append(f"suffix:{self.title_suffix}")

        return keywords

    def to_xmp_metadata(self) -> XMPMetadata:
        """Convert to XMP metadata structure."""
        # Join keywords list into comma-separated string for XMP metadata
        keywords_list = self.create_prefixed_keywords()
        keywords_string = ", ".join(keywords_list)

        # Use the aliased field names when creating XMPMetadata
        return XMPMetadata(
            **{
                "dc:title": self.title,  # Use the localized title directly
                "dc:creator": PDF_METADATA_DOCINFO_CREATOR,
                "dc:description": self.document_category_name,  # Map document_category_name to dc:description
                "pdf:Keywords": keywords_string,
                "pdf:Producer": PDF_METADATA_DOCINFO_PRODUCER,
                "xmp:CreatorTool": PDF_METADATA_DOCINFO_CREATOR,  # Match Info /Creator for ISO compliance
                # PDF/A-2b: Add xmpMM fields
                "xmpMM:DocumentID": f"uuid:{self.uuid}",  # Link to persistent UUID
                "xmpMM:InstanceID": f"uuid:{str(uuid.uuid4())}",  # New UUID for this specific PDF instance
                "hypodossier:DocumentCategoryKey": self.document_category_key,
                "hypodossier:TitleSuffix": self.title_suffix,
                "hypodossier:Version": self.version,
                "hypodossier:UUID": self.uuid,
            }
        )


def convert_semantic_document_to_metadata(
    document: Union[SemanticDocumentFullApiData, "SemanticDocument"],
    document_categories: Optional[Dict[str, Any]] = None,
) -> SemanticDocumentMetadata:
    """
    Convert either SemanticDocumentFullApiData or SemanticDocument to SemanticDocumentMetadata.

    This function centralizes all the messy type checking that was previously scattered
    throughout the PDF metadata generation code. It handles the differences between:
    - SemanticDocumentFullApiData (schema): has 'suffix', 'formatted_title'
    - SemanticDocument (Django model): has 'title_suffix', 'title' property

    The function normalizes these differences into a clean, lightweight schema that
    contains only the fields needed for PDF metadata generation.

    Args:
        document: Either a Pydantic schema object (SemanticDocumentFullApiData) or
                 Django model instance (SemanticDocument)
        document_categories: Optional pre-fetched document categories to avoid N+1 queries.
                           Should be account-specific to ensure correct localized titles.
                           If not provided, will attempt to fetch from document.dossier.account
                           or fall back to global categories.

    Returns:
        SemanticDocumentMetadata: Normalized metadata with consistent field names and
                                 pre-computed localized document category titles

    Example:
        # Works with Django model
        semantic_doc = SemanticDocument.objects.get(uuid=doc_uuid)
        metadata = convert_semantic_document_to_metadata(semantic_doc)

        # Works with schema object (e.g., from RabbitMQ message)
        schema_doc = SemanticDocumentFullApiData(**message_data)
        metadata = convert_semantic_document_to_metadata(schema_doc)

        # Both produce the same normalized result
        assert isinstance(metadata, SemanticDocumentMetadata)
    """
    # Step 1: Get document categories (account-specific if possible)
    if document_categories is None:
        # Try to get account-specific categories if we have a Django model instance
        # This ensures we get the correct localized titles for the account
        if hasattr(document, "dossier") and hasattr(document.dossier, "account"):
            document_categories = get_document_categories_by_name_for_account(
                document.dossier.account
            )
        else:
            # Last resort: use global categories (may have account boundary issues)
            # This happens when working with schema objects that don't have dossier relationship
            document_categories = get_document_categories_by_name()

    doc_cat_name = document.document_category.name
    doc_cat = document_categories[doc_cat_name]

    # Step 2: Normalize title - handle different attribute names between schema and model
    # This is one of the main pain points the old Union type caused
    title = None
    if hasattr(document, "formatted_title"):
        # SemanticDocumentFullApiData has pre-computed formatted_title
        title = document.formatted_title
    elif hasattr(document, "title"):
        # SemanticDocument model has title property (computed dynamically)
        title = document.title
    else:
        # Fallback: construct title from document category (shouldn't happen in practice)
        title = f"{document.document_category.id} {doc_cat.translated('de')}"

    # Step 3: Normalize title_suffix - another source of Union type complexity
    # Different types use different field names for the same concept
    title_suffix = None
    if hasattr(document, "suffix"):
        # SemanticDocumentFullApiData uses 'suffix' field
        title_suffix = document.suffix
    elif hasattr(document, "title_suffix"):
        # SemanticDocument model uses 'title_suffix' field
        title_suffix = document.title_suffix

    # Step 4: Create normalized metadata with pre-computed localized titles
    # This eliminates the need for future functions to handle Union types or do N+1 queries
    return SemanticDocumentMetadata(
        uuid=document.uuid,
        title=title,  # Normalized title from either formatted_title or title property
        title_suffix=title_suffix,  # Normalized suffix from either suffix or title_suffix
        document_category_key=doc_cat_name,
        document_category_name=doc_cat_name,  # Currently same as key
        # Pre-compute all localized titles to avoid repeated queries
        document_category_title_de=doc_cat.translated("de"),
        document_category_title_en=doc_cat.translated("en"),
        document_category_title_fr=doc_cat.translated("fr"),
        document_category_title_it=doc_cat.translated("it"),
    )


def create_pdf_metadata_docinfo(
    document: Union[SemanticDocumentFullApiData, "SemanticDocument"],
    document_categories: Optional[Dict[str, Any]] = None,
    dossier_language: Optional[str] = None,
) -> PdfMetadataDocinfo:
    """
    Create PDF docinfo metadata from semantic document.

    LEGACY FUNCTION: This function maintains backward compatibility with existing code
    that passes Union[SemanticDocumentFullApiData, "SemanticDocument"].

    For new code, prefer using:
    1. convert_semantic_document_to_metadata() to get normalized metadata
    2. create_pdf_metadata_docinfo_from_metadata() for clean API

    This approach is more efficient and maintainable.

    Args:
        document: Semantic document data (schema object or model instance)
        document_categories: Optional pre-fetched document categories to avoid N+1 queries.
                           Should be account-specific to ensure correct titles.
        dossier_language: Optional dossier language for localization

    Returns:
        PdfMetadataDocinfo object with document metadata
    """
    # Convert to normalized metadata first - this centralizes all the Union type handling
    metadata = convert_semantic_document_to_metadata(document, document_categories)

    # Determine dossier language with priority order:
    # 1. Explicitly passed dossier_language parameter
    # 2. Extract from Django model instances via semantic pages
    # 3. Default fallback to "de"
    dossier_lang = "de"  # Default fallback

    if dossier_language:
        # Use explicitly passed dossier language (highest priority)
        dossier_lang = dossier_language.lower()
    else:
        # Try to extract from semantic pages for Django model instances
        semantic_pages = None
        if hasattr(document, "semantic_pages"):
            if hasattr(document.semantic_pages, "all"):
                # Django RelatedManager - call .all() to get queryset
                semantic_pages = document.semantic_pages.all()
            else:
                # List from schema object
                semantic_pages = document.semantic_pages

        if semantic_pages and len(semantic_pages) > 0:
            # Only extract language from dossier for Django model instances
            first_page = semantic_pages[0]
            if hasattr(first_page, "dossier"):
                # Django model instance - access through dossier relationship
                dossier_lang = first_page.dossier.lang.lower()

    # Create localized title using the dossier language
    # This follows the same logic as SemanticDocument.calculate_title
    title_custom = getattr(document, "title_custom", None)
    if title_custom:
        localized_title = title_custom
    else:
        # Use the localized category title from metadata
        localized_category_name = getattr(
            metadata, f"document_category_title_{dossier_lang}"
        )
        localized_title = f"{document.document_category.id} {localized_category_name}"

        if metadata.title_suffix:
            # Strip commas from suffix_value for consistency in title
            clean_suffix_value = metadata.title_suffix.replace(",", "")
            localized_title += f" {clean_suffix_value}"

    # Strip commas from title_suffix to prevent keyword parsing issues
    title_suffix = metadata.title_suffix
    if title_suffix:
        title_suffix = title_suffix.replace(",", "")

    return PdfMetadataDocinfo(
        uuid=str(metadata.uuid),
        title=localized_title,
        document_category_key=metadata.document_category_key,
        document_category_name=getattr(
            metadata, f"document_category_title_{dossier_lang}"
        ),
        document_category_title_de=metadata.document_category_title_de,
        document_category_title_en=metadata.document_category_title_en,
        document_category_title_fr=metadata.document_category_title_fr,
        document_category_title_it=metadata.document_category_title_it,
        title_suffix=title_suffix,
    )


def create_pdf_metadata_docinfo_from_metadata(
    metadata: SemanticDocumentMetadata,
    dossier_language: Optional[str] = None,
) -> PdfMetadataDocinfo:
    """
    Create PDF docinfo metadata from SemanticDocumentMetadata.

    This is the clean, new API that works with the normalized metadata schema.
    It eliminates all the messy Union type handling and hasattr() checks that
    were present in the original create_pdf_metadata_docinfo() function.

    This function is much simpler and more maintainable because:
    - No need to handle different types with hasattr() checks
    - All localized titles are pre-computed in the metadata
    - Field names are normalized and consistent
    - Type safety is guaranteed by Pydantic validation

    Args:
        metadata: Normalized semantic document metadata containing all required fields
                 with consistent naming and pre-computed localized titles
        dossier_language: Optional dossier language for localization (e.g., 'de', 'en', 'fr', 'it').
                         Defaults to 'de' if not provided.

    Returns:
        PdfMetadataDocinfo: Object containing structured metadata for PDF generation
                           with prefixed keywords and XMP support

    Example:
        # Convert document to metadata first
        metadata = convert_semantic_document_to_metadata(document)

        # Use clean API
        pdf_docinfo = create_pdf_metadata_docinfo_from_metadata(metadata, "de")

        # Much cleaner than the old approach:
        # pdf_docinfo = create_pdf_metadata_docinfo(document, categories, "de")
    """
    # Determine dossier language with fallback to "de"
    dossier_lang = dossier_language.lower() if dossier_language else "de"

    # Create localized title using the dossier language
    localized_category_name = getattr(
        metadata, f"document_category_title_{dossier_lang}"
    )
    localized_title = f"{metadata.document_category_key} {localized_category_name}"

    if metadata.title_suffix:
        # Strip commas from suffix_value for consistency in title
        clean_suffix_value = metadata.title_suffix.replace(",", "")
        localized_title += f" {clean_suffix_value}"

    # Strip commas from title_suffix to prevent keyword parsing issues
    title_suffix = metadata.title_suffix
    if title_suffix:
        title_suffix = title_suffix.replace(",", "")

    return PdfMetadataDocinfo(
        uuid=str(metadata.uuid),
        title=localized_title,
        document_category_key=metadata.document_category_key,
        document_category_name=getattr(
            metadata, f"document_category_title_{dossier_lang}"
        ),
        document_category_title_de=metadata.document_category_title_de,
        document_category_title_en=metadata.document_category_title_en,
        document_category_title_fr=metadata.document_category_title_fr,
        document_category_title_it=metadata.document_category_title_it,
        title_suffix=title_suffix,
    )


def create_document_info_properties_with_prefixed_keywords(
    metadata: PdfMetadataDocinfo,
) -> Dict[str, Any]:
    """
    Create PDF document info properties with prefixed keywords.
    Uses standard /Keywords field with namespace prefixes for reliable parsing.
    """
    # Join keywords list into comma-separated string for PDF document info
    keywords_list = metadata.create_prefixed_keywords()
    keywords_string = ", ".join(keywords_list)

    return {
        "/Title": metadata.title,  # Use the localized title directly (already includes suffix if present)
        "/Subject": metadata.document_category_name,  # Use the same localized title
        "/Keywords": keywords_string,
        "/Creator": PDF_METADATA_DOCINFO_CREATOR,
        "/Producer": PDF_METADATA_DOCINFO_PRODUCER,
    }


def parse_document_info_properties_with_prefixed_keywords(
    pdf_info: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Parse PDF document info properties from prefixed keywords format.
    Reliably extracts structured data from prefixed keywords.
    """
    keywords_str = pdf_info.get("/Keywords", "")
    parsed_keywords = ParsedKeywords.from_keywords_string(keywords_str)

    return {
        "title": pdf_info.get("/Title", ""),
        "author": pdf_info.get("/Author", ""),
        "subject": pdf_info.get("/Subject", ""),
        "creator": pdf_info.get("/Creator", ""),
        "producer": pdf_info.get("/Producer", ""),
        "structured_metadata": parsed_keywords.structured,
        "searchable_keywords": parsed_keywords.searchable,
        "document_category_key": parsed_keywords.structured.get("category"),
        "document_type": parsed_keywords.structured.get("type"),
        "title_suffix": parsed_keywords.structured.get("suffix"),
        "uuid": parsed_keywords.structured.get("id"),
    }


def add_xmp_metadata_to_pdf(writer: PdfWriter, metadata: PdfMetadataDocinfo) -> None:
    """
    Add XMP metadata to PDF writer.
    Creates structured XMP metadata with custom namespace support.
    """
    xmp_metadata = metadata.to_xmp_metadata()
    xmp_xml = xmp_metadata.to_xmp_xml()

    xmp_data = xmp_xml.encode("utf-8")

    xmp_stream = StreamObject()
    xmp_stream.update(
        {
            NameObject("/Type"): NameObject("/Metadata"),
            NameObject("/Subtype"): NameObject("/XML"),
            NameObject("/Length"): NumberObject(len(xmp_data)),
        }
    )
    xmp_stream._data = xmp_data

    # Add to PDF catalog - always replace any existing metadata with XMP metadata
    metadata_ref = writer._add_object(xmp_stream)
    writer._root_object.update({NameObject("/Metadata"): metadata_ref})


def read_xmp_metadata_from_pdf(
    pdf_reader: pypdf.PdfReader,
) -> Optional[Dict[str, Any]]:
    """
    Read XMP metadata from PDF reader.
    Parses XMP XML and extracts structured metadata.
    """
    try:
        # XMP metadata is stored in the PDF catalog, not in the standard metadata
        catalog = cast(DictionaryObject, pdf_reader.trailer["/Root"])
        if catalog.get("/Metadata") is None:
            return None

        metadata_obj = catalog.get("/Metadata")
        # Get the actual object if it's an indirect reference
        # metadata_obj could be None if "/Metadata" was not found or was not a direct object
        if metadata_obj is not None and hasattr(metadata_obj, "get_object"):
            metadata_obj = metadata_obj.get_object()

        # Explicitly cast metadata_obj to StreamObject before accessing _data
        # This resolves Pylance warning about _data not being known attribute of PdfObject
        if not isinstance(metadata_obj, StreamObject):  # Ensure it's a StreamObject
            logger.warning(
                "Metadata object is not a StreamObject, cannot extract XMP data"
            )
            return None

        xmp_data = metadata_obj._data

        # Parse XMP XML
        xmp_str = xmp_data.decode("utf-8")

        # Remove XMP packet wrapper - handle different namespace prefixes
        # Look for xmpmeta with any namespace prefix (x:, ns0:, etc.)

        xmpmeta_pattern = r"<[^:]*:xmpmeta[^>]*>"
        xmpmeta_end_pattern = r"</[^:]*:xmpmeta>"

        start_match = re.search(xmpmeta_pattern, xmp_str)
        end_match = re.search(xmpmeta_end_pattern, xmp_str)

        if not start_match or not end_match:
            return None

        start_idx = start_match.start()
        end_idx = end_match.end()
        xml_content = xmp_str[start_idx:end_idx]
        root = ET.fromstring(xml_content)

        # Extract metadata from XMP
        namespaces = {
            "x": "adobe:ns:meta/",
            "rdf": "http://www.w3.org/1999/02/22-rdf-syntax-ns#",
            "dc": "http://purl.org/dc/elements/1.1/",
            "pdf": "http://ns.adobe.com/pdf/1.3/",
            "xmp": "http://ns.adobe.com/xap/1.0/",
            "hypodossier": "http://hypodossier.com/ns/1.0/",
        }

        # Find all rdf:Description elements - there may be multiple
        descriptions = root.findall(".//rdf:Description", namespaces)
        if not descriptions:
            return None

        metadata = {}

        # Extract metadata from all description elements
        for description in descriptions:
            # Extract standard fields
            for field in ["title", "creator", "description"]:
                elem = description.find(f"dc:{field}", namespaces)
                if elem is not None:
                    # Handle rdf:Alt structure for title and description
                    if field in ["title", "description"]:
                        alt = elem.find("rdf:Alt", namespaces)
                        if alt is not None:
                            li = alt.find("rdf:li", namespaces)
                            if li is not None:
                                metadata[f"dc_{field}"] = li.text
                    # Handle rdf:Seq structure for creator
                    elif field == "creator":
                        seq = elem.find("rdf:Seq", namespaces)
                        if seq is not None:
                            li = seq.find("rdf:li", namespaces)
                            if li is not None:
                                metadata[f"dc_{field}"] = li.text
                    else:
                        metadata[f"dc_{field}"] = elem.text

            # Extract subject array
            subject_elem = description.find("dc:subject", namespaces)
            if subject_elem is not None:
                bag = subject_elem.find("rdf:Bag", namespaces)
                if bag is not None:
                    subjects = [li.text for li in bag.findall("rdf:li", namespaces)]
                    metadata["dc_subject"] = subjects

            # Extract PDF fields
            for field in ["Keywords", "Producer"]:
                elem = description.find(f"pdf:{field}", namespaces)
                if elem is not None:
                    metadata[f"pdf_{field.lower()}"] = elem.text

            # Extract XMP fields
            for field in ["CreatorTool"]:
                elem = description.find(f"xmp:{field}", namespaces)
                if elem is not None:
                    metadata[f"xmp_{field.lower()}"] = elem.text

            # Extract HypoDossier custom fields
            for field in ["DocumentCategoryKey", "TitleSuffix", "Version", "UUID"]:
                elem = description.find(f"hypodossier:{field}", namespaces)
                if elem is not None:
                    metadata[f"hypodossier_{field.lower()}"] = elem.text

        return metadata

    except Exception as e:
        logger.warning("Failed to parse XMP metadata", error=str(e))
        return None


def _add_trailer_id(writer: PdfWriter, uuid_str: str) -> None:
    """
    Add required /ID entry to PDF trailer for PDF/A-2b compliance.

    PDF/A-2b requires an /ID entry in the file trailer consisting of an array
    of two byte strings. The first is a permanent identifier for the document,
    the second changes with each modification.

    Args:
        writer: PDF writer instance
        uuid_str: Document UUID string to derive the ID from
    """
    try:
        # Strip hyphens, take first 16 bytes (32 hex chars) for File Identifier
        hexstr = uuid_str.replace("-", "")[:32]
        if len(hexstr) < 32:  # Pad if too short, though UUIDs should be long enough
            hexstr = hexstr.ljust(32, "0")

        # Create the first ID (permanent document identifier)
        id_bytes = bytes.fromhex(hexstr)
        id_obj1 = ByteStringObject(id_bytes)

        # Create the second ID (modification identifier) - same for new documents
        id_obj2 = ByteStringObject(id_bytes)

        # PDF/A-2b requires an array of two byte-strings
        id_array = ArrayObject([id_obj1, id_obj2])

        # Set the _ID attribute on the writer for pypdf to handle trailer
        writer._ID = id_array
        logger.debug(f"Added trailer ID array with {len(id_bytes)} bytes each")

    except Exception as e:
        logger.warning(f"Failed to add trailer ID: {e}")
        # Fallback: create a simple ID based on current time if UUID fails
        import time

        fallback_str = f"{int(time.time())}{uuid_str[:8]}".ljust(32, "0")[:32]
        fallback_bytes = bytes.fromhex(fallback_str)
        fallback_obj = ByteStringObject(fallback_bytes)
        writer._ID = ArrayObject([fallback_obj, fallback_obj])


def add_srgb_output_intent(writer: PdfWriter) -> None:
    """
    Add sRGB output intent to PDF writer for PDF/A-2b compliance.

    This is required when DeviceRGB color space is used in PDF/A-2 documents.
    Creates a minimal sRGB output intent that satisfies PDF/A-2b validation.

    Args:
        writer: PDF writer instance
    """
    # Create a minimal sRGB output intent without embedding a full ICC profile
    # This approach creates a reference to the standard sRGB color space
    output_intent = DictionaryObject(
        {
            NameObject("/Type"): NameObject("/OutputIntent"),
            NameObject("/S"): NameObject(
                "/GTS_PDFA2"
            ),  # PDF/A-2 output intent for PDF/A-2b compliance
            NameObject("/OutputConditionIdentifier"): TextStringObject(
                "sRGB IEC61966-2.1"
            ),
            NameObject("/Info"): TextStringObject("sRGB IEC61966-2.1"),
            NameObject("/OutputCondition"): TextStringObject("sRGB IEC61966-2.1"),
            NameObject("/RegistryName"): TextStringObject("http://www.color.org"),
        }
    )
    output_intent_ref = writer._add_object(output_intent)

    # Add OutputIntents array to catalog
    if NameObject("/OutputIntents") not in writer._root_object:
        writer._root_object.update(
            {NameObject("/OutputIntents"): ArrayObject([output_intent_ref])}
        )
    else:
        # Append to existing OutputIntents if any
        existing_intents = writer._root_object[NameObject("/OutputIntents")]
        existing_intents.append(output_intent_ref)


def disable_image_interpolation(writer: PdfWriter) -> None:
    """
    Disable image interpolation for PDF/A-2b compliance.

    PDF/A-2b requires that if an Image dictionary contains the Interpolate key,
    its value must be false. This function recursively searches for all image
    objects including those nested in Form XObjects.

    Args:
        writer: PDF writer instance
    """

    def _process_xobject_recursively(xobj_dict):
        """Recursively process XObject dictionary to find all images."""
        if not xobj_dict:
            return

        # Handle indirect references
        if hasattr(xobj_dict, "get_object"):
            xobj_dict = xobj_dict.get_object()

        if not hasattr(xobj_dict, "items"):
            return

        for name, xobj_ref in xobj_dict.items():
            # Get the actual XObject
            xobj = (
                xobj_ref.get_object() if hasattr(xobj_ref, "get_object") else xobj_ref
            )

            if not isinstance(xobj, dict):
                continue

            # Check if this is an Image XObject
            subtype = xobj.get("/Subtype")
            if subtype == "/Image":
                xobj[NameObject("/Interpolate")] = BooleanObject(False)
                logger.debug(f"Ensured interpolation is disabled for image: {name}")

            # If this is a Form XObject, recursively process its resources
            elif subtype == "/Form":
                form_resources = xobj.get("/Resources")
                if form_resources:
                    if hasattr(form_resources, "get_object"):
                        form_resources = form_resources.get_object()
                    form_xobjs = form_resources.get("/XObject", {})
                    _process_xobject_recursively(form_xobjs)

    # Process all pages
    for page_num, page in enumerate(writer.pages):
        resources = page.get("/Resources")
        if not resources:
            continue

        # Handle indirect references
        if hasattr(resources, "get_object"):
            resources = resources.get_object()

        # Process XObjects in page resources
        xobjs = resources.get("/XObject", {})
        _process_xobject_recursively(xobjs)


def remove_font_differences(writer: PdfWriter) -> None:
    """
    Remove /Differences arrays from TrueType font encodings for PDF/A-2b compliance.

    PDF/A-2b requires that non-symbolic TrueType fonts use MacRoman or WinAnsi
    encoding without a /Differences array. This function also attempts to fix
    common TrueType font issues that can cause VeraPDF warnings.

    Args:
        writer: PDF writer instance
    """

    def _process_font_resources(fonts_dict):
        """Process font resources recursively."""
        if not fonts_dict:
            return

        # Handle indirect references
        if hasattr(fonts_dict, "get_object"):
            fonts_dict = fonts_dict.get_object()

        if not hasattr(fonts_dict, "items"):
            return

        for font_name, font_ref in fonts_dict.items():
            try:
                font = (
                    font_ref.get_object()
                    if hasattr(font_ref, "get_object")
                    else font_ref
                )

                if not isinstance(font, dict):
                    continue

                # Remove /Differences arrays from encoding
                enc = font.get("/Encoding")
                if isinstance(enc, DictionaryObject) and "/Differences" in enc:
                    del enc["/Differences"]
                    logger.debug(f"Removed /Differences from font: {font_name}")

                # Additional TrueType font fixes for PDF/A-2b compliance
                subtype = font.get("/Subtype")
                if subtype == "/TrueType":
                    # Ensure proper encoding for TrueType fonts
                    if "/Encoding" not in font:
                        # Set default encoding for TrueType fonts
                        font[NameObject("/Encoding")] = NameObject("/WinAnsiEncoding")
                        logger.debug(
                            f"Added WinAnsiEncoding to TrueType font: {font_name}"
                        )

            except Exception as e:
                logger.warning(f"Failed to process font {font_name}: {e}")

    # Process all pages and their font resources
    for page_num, page in enumerate(writer.pages):
        resources = page.get("/Resources")
        if not resources:
            continue

        # Get the actual resources object if it's an indirect reference
        if hasattr(resources, "get_object"):
            resources = resources.get_object()

        # Process fonts in page resources
        fonts = resources.get("/Font", {})
        _process_font_resources(fonts)


# Combined implementation functions
def add_metadata_docinfo_to_pdfwriter(
    writer: PdfWriter,
    document: Union[SemanticDocumentFullApiData, "SemanticDocument"],
    document_categories: Optional[Dict[str, Any]] = None,
    dossier_language: Optional[str] = None,
) -> None:
    """
    Add metadata docinfo to PDF using both prefixed keywords and XMP.

    LEGACY FUNCTION: This function maintains backward compatibility with Union type parameters.
    Internally, it uses create_pdf_metadata_docinfo() which converts to SemanticDocumentMetadata
    to eliminate the Union type complexity.

    This function combines two metadata approaches:
    1. Prefixed keywords in standard PDF fields (e.g., "category:PASSPORT_CH" in /Keywords)
    2. Structured XMP metadata with custom namespace support

    Additionally applies all PDF/A-2b compliance fixes:
    - Sets PDF version to 1.7 for PDF/A-2b compliance
    - Adds required /ID entry to PDF trailer (6.1.3-1)
    - Embeds sRGB OutputIntent for DeviceRGB usage (6.2.3.3-1)
    - Disables image interpolation (6.2.4-3)
    - Removes font differences from TrueType fonts (6.3.7-1)

    Args:
        writer: PDF writer instance
        document: Semantic document data (schema object or model instance)
                 Union type handling is done internally via conversion to SemanticDocumentMetadata
        document_categories: Optional pre-fetched document categories to avoid repeated queries
        dossier_language: Optional dossier language to use for localization (e.g., 'de', 'en', 'fr', 'it')
    """
    try:
        metadata = create_pdf_metadata_docinfo(
            document, document_categories, dossier_language
        )
        logger.debug("Created PDF metadata docinfo successfully")
    except Exception as e:
        logger.error(f"Failed to create PDF metadata docinfo: {e}")
        return

    try:
        # Add metadata (prefixed keywords in standard fields)
        doc_info = create_document_info_properties_with_prefixed_keywords(metadata)
        writer.add_metadata(doc_info)
        logger.debug("Added standard PDF docinfo metadata with prefixed keywords")
    except Exception as e:
        logger.error(f"Failed to add standard PDF docinfo metadata: {e}")

    try:
        # Add XMP metadata for structured data
        add_xmp_metadata_to_pdf(writer, metadata)
        logger.debug("Added PDF XMP metadata successfully")
    except Exception as e:
        logger.error(f"Failed to add XMP metadata: {e}")

    try:
        # Set PDF version to 1.7 for PDF/A-2b compliance
        # Different pypdf versions use different methods
        if hasattr(writer, "set_min_version"):
            # pypdf 3.0.0+ method
            writer.set_min_version("1.7")
            logger.debug("Set PDF version to 1.7 using set_min_version")
        elif hasattr(writer, "pdf_header"):
            # pypdf 5.6.0 method - directly set the header
            writer.pdf_header = "%PDF-1.7"
            logger.debug("Set PDF version to 1.7 using pdf_header")
        else:
            logger.warning(
                "pypdf version does not support setting PDF version to 1.7 for PDF/A-2b. "
                "Header might not be compliant."
            )
    except Exception as e:
        logger.error(f"Failed to set PDF version: {e}")

    try:
        # Add required /ID entry to trailer for PDF/A-2b compliance
        _add_trailer_id(writer, metadata.uuid)
        logger.debug("Added trailer ID successfully")
    except Exception as e:
        logger.error(f"Failed to add trailer ID: {e}")

    try:
        # Add sRGB output intent for PDF/A-2b compliance with DeviceRGB
        add_srgb_output_intent(writer)
        logger.debug("Added sRGB output intent successfully")
    except Exception as e:
        logger.error(f"Failed to add sRGB output intent: {e}")

    try:
        # Fix image interpolation for PDF/A-2b compliance
        disable_image_interpolation(writer)
        logger.debug("Disabled image interpolation successfully")
    except Exception as e:
        logger.error(f"Failed to disable image interpolation: {e}")

    try:
        # Remove font differences for PDF/A-2b compliance
        remove_font_differences(writer)
        logger.debug("Removed font differences successfully")
    except Exception as e:
        logger.error(f"Failed to remove font differences: {e}")


def has_hypodossier_metadata_docinfo_from_reader(pdf_reader: pypdf.PdfReader) -> bool:
    """
    Check if a PDF reader contains valid Hypodossier metadata using Pydantic schema validation.

    This function uses the existing Pydantic models to validate that the metadata
    conforms to the expected Hypodossier schema structure, ensuring we detect
    properly structured metadata rather than coincidental matches.

    Args:
        pdf_reader: pypdf PdfReader instance

    Returns:
        True if valid Hypodossier metadata is found, False otherwise
    """
    try:
        metadata = read_metadata_docinfo_from_pdf(pdf_reader)

        # Method 1: Validate prefixed keywords using ParsedKeywords schema
        prefixed_metadata = metadata.get("prefixed_keywords_metadata", {})
        structured_metadata = prefixed_metadata.get("structured_metadata", {})

        # Check for required Hypodossier prefixed keywords with proper validation
        if structured_metadata:
            try:
                # Validate that we have the core Hypodossier keywords
                has_category = "category" in structured_metadata
                has_id = "id" in structured_metadata

                # Additional validation: check if the category looks like a valid document category
                if has_category and has_id:
                    category_value = structured_metadata.get("category", "")
                    id_value = structured_metadata.get("id", "")

                    # Basic validation: category should be uppercase and ID should be UUID-like
                    if (
                        category_value
                        and category_value.isupper()
                        and id_value
                        and len(id_value) >= 32
                    ):  # UUID without hyphens is 32 chars
                        logger.debug("Found valid Hypodossier prefixed keywords")
                        return True

            except Exception as e:
                logger.debug(f"Prefixed keywords validation failed: {e}")

        # Method 2: Validate XMP metadata using XMPMetadata schema
        xmp_metadata = metadata.get("xmp_metadata", {})
        if xmp_metadata:
            try:
                # Check for Hypodossier XMP namespace fields
                hypodossier_fields = {
                    k: v
                    for k, v in xmp_metadata.items()
                    if k.startswith("hypodossier_") and v is not None
                }

                if hypodossier_fields:
                    # Validate core Hypodossier XMP fields
                    has_doc_category = (
                        "hypodossier_documentcategorykey" in hypodossier_fields
                    )
                    has_uuid = "hypodossier_uuid" in hypodossier_fields

                    if has_doc_category and has_uuid:
                        # Additional validation: check if values look valid
                        doc_category = hypodossier_fields.get(
                            "hypodossier_documentcategorykey", ""
                        )
                        uuid_value = hypodossier_fields.get("hypodossier_uuid", "")
                        version = hypodossier_fields.get("hypodossier_version", "")

                        # Validate that these look like proper Hypodossier values
                        if (
                            doc_category
                            and doc_category.isupper()
                            and uuid_value
                            and len(uuid_value) >= 32
                            and version
                            and version.startswith("1.")
                        ):  # Version should start with 1.
                            logger.debug("Found valid Hypodossier XMP metadata")
                            return True

            except Exception as e:
                logger.debug(f"XMP metadata validation failed: {e}")

        # Method 3: Validate standard metadata for Hypodossier creator
        standard_metadata = metadata.get("standard_metadata", {})
        if standard_metadata:
            creator = standard_metadata.get("/Creator", "")
            producer = standard_metadata.get("/Producer", "")

            # Check for exact "hypodossier" string (case-insensitive) in creator or producer
            creator_lower = creator.lower() if creator else ""
            producer_lower = producer.lower() if producer else ""

            if "hypodossier" in creator_lower and "hypodossier" in producer_lower:
                # Additional validation: check if there are also keywords that suggest Hypodossier
                keywords = standard_metadata.get("/Keywords", "")
                if keywords and ("category:" in keywords or "id:" in keywords):
                    logger.debug("Found valid Hypodossier creator with keywords")
                    return True

        return False

    except Exception as e:
        logger.warning(f"Failed to check Hypodossier metadata: {e}")
        return False


def try_reconstruct_hypodossier_metadata_from_pdf(
    pdf_reader: pypdf.PdfReader,
) -> Optional[Dict[str, Any]]:
    """
    Attempt to reconstruct Hypodossier metadata from a PDF using schema validation.

    This function tries to extract and validate enough metadata to reconstruct
    a partial PdfMetadataDocinfo-like structure, providing stronger validation
    than simple pattern matching.

    Args:
        pdf_reader: pypdf PdfReader instance

    Returns:
        Dictionary with reconstructed metadata if valid Hypodossier metadata found, None otherwise
    """
    try:
        metadata = read_metadata_docinfo_from_pdf(pdf_reader)
        reconstructed = {}

        # Try to extract from prefixed keywords first
        prefixed_metadata = metadata.get("prefixed_keywords_metadata", {})
        structured_metadata = prefixed_metadata.get("structured_metadata", {})

        if structured_metadata:
            # Map prefixed keywords to schema fields
            if "category" in structured_metadata:
                reconstructed["document_category_key"] = structured_metadata["category"]
            if "id" in structured_metadata:
                reconstructed["uuid"] = structured_metadata["id"]
            if "suffix" in structured_metadata:
                reconstructed["title_suffix"] = structured_metadata["suffix"]

        # Try to extract from XMP metadata
        xmp_metadata = metadata.get("xmp_metadata", {})
        if xmp_metadata:
            # Map XMP fields to schema fields
            if "hypodossier_documentcategorykey" in xmp_metadata:
                reconstructed["document_category_key"] = xmp_metadata[
                    "hypodossier_documentcategorykey"
                ]
            if "hypodossier_uuid" in xmp_metadata:
                reconstructed["uuid"] = xmp_metadata["hypodossier_uuid"]
            if "hypodossier_titlesuffix" in xmp_metadata:
                reconstructed["title_suffix"] = xmp_metadata["hypodossier_titlesuffix"]
            if "hypodossier_version" in xmp_metadata:
                reconstructed["version"] = xmp_metadata["hypodossier_version"]

        # Extract from standard metadata
        standard_metadata = metadata.get("standard_metadata", {})
        if standard_metadata:
            if "/Title" in standard_metadata:
                reconstructed["title"] = standard_metadata["/Title"]
            if "/Subject" in standard_metadata:
                reconstructed["document_category_name"] = standard_metadata["/Subject"]

        # Validate that we have minimum required fields for Hypodossier metadata
        required_fields = ["document_category_key", "uuid"]
        if all(field in reconstructed for field in required_fields):
            # Additional validation using the actual schema constraints
            try:
                # Validate UUID format (basic check)
                uuid_value = reconstructed["uuid"]
                if len(uuid_value.replace("-", "")) >= 32:  # Valid UUID length
                    # Validate document category (should be uppercase)
                    doc_category = reconstructed["document_category_key"]
                    if doc_category and doc_category.isupper():
                        logger.debug(
                            "Successfully reconstructed valid Hypodossier metadata"
                        )
                        return reconstructed
            except Exception as e:
                logger.debug(f"Metadata validation failed: {e}")

        return None

    except Exception as e:
        logger.debug(f"Failed to reconstruct Hypodossier metadata: {e}")
        return None


def has_hypodossier_metadata(pdf_path: Path) -> bool:
    """
    Check if a PDF file contains Hypodossier metadata.

    Args:
        pdf_path: Path to the PDF file

    Returns:
        True if Hypodossier metadata is found, False otherwise
    """
    try:
        with open(pdf_path, "rb") as f:
            pdf_reader = pypdf.PdfReader(f)
            return has_hypodossier_metadata_docinfo_from_reader(pdf_reader)
    except Exception as e:
        logger.warning(f"Failed to check metadata in PDF {pdf_path}: {e}")
        return False


def read_metadata_docinfo_from_pdf(pdf_reader: pypdf.PdfReader) -> Dict[str, Any]:
    """
    Read metadata from PDF using both prefixed keywords and XMP.

    Returns:
        Dictionary containing all available metadata from both sources
    """
    result = {
        "prefixed_keywords_metadata": {},
        "xmp_metadata": {},
        "standard_metadata": {},
    }

    # Read standard PDF info
    if pdf_reader.metadata:
        standard_info = {k: v for k, v in pdf_reader.metadata.items()}
        result["standard_metadata"] = standard_info

        # Parse prefixed keywords if keywords are present
        if "/Keywords" in standard_info:
            result["prefixed_keywords_metadata"] = (
                parse_document_info_properties_with_prefixed_keywords(standard_info)
            )

    # Read XMP metadata
    xmp_data = read_xmp_metadata_from_pdf(pdf_reader)
    if xmp_data:
        result["xmp_metadata"] = xmp_data

    return result
