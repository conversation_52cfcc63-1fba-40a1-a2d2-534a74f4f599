"""
Test script for PDF metadata docinfo implementations.
Demonstrates both Option A (prefixed keywords) and XMP metadata.
"""

import tempfile
from pathlib import Path

import pytest
from pypdf import PdfWriter, PdfReader
from reportlab.pdfgen import canvas
from io import BytesIO

from dossier.fakes import (
    load_initial_document_categories,
    add_some_fake_semantic_documents,
)
from dossier.models import DocumentCategory
from semantic_document.pdf_metadata_docinfo import (
    PdfMetadataDocinfo,
    ParsedKeywords,
    PrefixedKeyword,
    create_document_info_properties_with_prefixed_keywords,
    parse_document_info_properties_with_prefixed_keywords,
    read_metadata_docinfo_from_pdf,
    create_pdf_metadata_docinfo,
    PDF_METADATA_DOCINFO_CREATOR,
    add_metadata_docinfo_to_pdfwriter,
)

from dossier.conftest import synthetic_dossier  # noqa: F401


@pytest.fixture
def dossier_with_documents(synthetic_dossier):
    """Create a dossier with document categories and semantic documents."""
    account = synthetic_dossier.account

    # Load document categories
    load_initial_document_categories(account=account)

    # Get a document category
    document_category = DocumentCategory.objects.filter(account=account).first()

    # Create semantic documents
    semantic_documents = add_some_fake_semantic_documents(
        synthetic_dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=[document_category.name],
        max_pages=5,
        min_num_pages=5,
    )

    return {
        "dossier": synthetic_dossier,
        "account": account,
        "document_category": document_category,
        "semantic_documents": semantic_documents,
    }


@pytest.mark.django_db
def test_pydantic_models():
    """Test Pydantic 2 models for data validation."""

    # Test PrefixedKeyword
    keyword = PrefixedKeyword(prefix="category", value="PASSPORT_CH")
    print(f"Prefixed keyword: {keyword}")

    # Test parsing from string
    parsed = PrefixedKeyword.from_string("suffix:renewal")
    print(f"Parsed keyword: {parsed}")

    # Test ParsedKeywords
    keywords_str = (
        "category:PASSPORT_CH, type:Swiss Passport, suffix:renewal, document, official"
    )
    parsed_keywords = ParsedKeywords.from_keywords_string(keywords_str)
    print(f"Structured metadata: {parsed_keywords.structured}")
    print(f"Searchable keywords: {parsed_keywords.searchable}")

    # Test PdfMetadataDocinfo
    metadata = PdfMetadataDocinfo(
        uuid="********-1234-5678-9012-********9abc",
        title="Swiss Passport",
        document_category_key="PASSPORT_CH",
        document_category_name="Swiss Passport",
        document_category_title_de="Schweizer Pass",
        document_category_title_en="Swiss Passport",
        document_category_title_fr="Passeport Suisse",
        document_category_title_it="Passaporto Svizzero",
        title_suffix="renewal",
    )

    print(f"Full title: {metadata.get_full_title()}")
    print(f"Prefixed keywords: {metadata.create_prefixed_keywords()}")


@pytest.mark.django_db
def test_metadata_implementation():
    """Test metadata implementation with prefixed keywords."""

    # Create test metadata
    metadata = PdfMetadataDocinfo(
        uuid="********-1234-5678-9012-********9abc",
        title="Swiss Passport",
        document_category_key="PASSPORT_CH",
        document_category_name="Swiss Passport",
        document_category_title_de="Schweizer Pass",
        document_category_title_en="Swiss Passport",
        document_category_title_fr="Passeport Suisse",
        document_category_title_it="Passaporto Svizzero",
        title_suffix="renewal",
    )

    # Create PDF info properties
    pdf_info = create_document_info_properties_with_prefixed_keywords(metadata)
    print("Generated PDF info properties:")
    for key, value in pdf_info.items():
        print(f"  {key}: {value}")

    # Test parsing back
    parsed = parse_document_info_properties_with_prefixed_keywords(pdf_info)
    print("\nParsed metadata:")
    for key, value in parsed.items():
        print(f"  {key}: {value}")

    # Verify reliability
    assert parsed["document_category_key"] == "PASSPORT_CH"
    assert parsed["title_suffix"] == "renewal"
    print("\n✅ Option A parsing is reliable!")


@pytest.mark.django_db
def test_xmp_implementation():
    """Test XMP metadata implementation."""
    print("\n=== Testing XMP Metadata ===")

    # Create test metadata
    metadata = PdfMetadataDocinfo(
        uuid="********-1234-5678-9012-********9abc",
        title="Swiss Passport",
        document_category_key="PASSPORT_CH",
        document_category_name="Swiss Passport",
        document_category_title_de="Schweizer Pass",
        document_category_title_en="Swiss Passport",
        document_category_title_fr="Passeport Suisse",
        document_category_title_it="Passaporto Svizzero",
        title_suffix="renewal",
    )

    # Create XMP metadata
    xmp_metadata = metadata.to_xmp_metadata()
    xmp_xml = xmp_metadata.to_xmp_xml()

    print("Generated XMP XML (first 500 chars):")
    print(xmp_xml[:500] + "..." if len(xmp_xml) > 500 else xmp_xml)

    # Verify XMP contains expected elements
    assert "hypodossier:DocumentCategoryKey" in xmp_xml
    assert "PASSPORT_CH" in xmp_xml
    assert "hypodossier:TitleSuffix" in xmp_xml
    assert "renewal" in xmp_xml
    print("\n✅ XMP metadata generation successful!")


@pytest.mark.django_db
def test_localized_title_generation(dossier_with_documents):
    """Test that localized titles are generated correctly for different languages."""
    print("\n=== Testing Localized Title Generation ===")

    # Get the semantic document from the fixture
    semantic_document = dossier_with_documents["semantic_documents"][0]

    # Test creating metadata from real semantic document
    metadata = create_pdf_metadata_docinfo(semantic_document)

    print(f"✅ Real document title: {metadata.title}")
    print(f"✅ Document category: {metadata.document_category_name}")
    print(f"✅ Document category key: {metadata.document_category_key}")

    # Verify metadata structure
    assert metadata.uuid == str(semantic_document.uuid)
    assert metadata.document_category_key == semantic_document.document_category.name
    assert metadata.title is not None
    assert metadata.document_category_name is not None

    # Test that we can create prefixed keywords
    keywords = metadata.create_prefixed_keywords()
    assert any(keyword.startswith("category:") for keyword in keywords)
    print(f"✅ Generated keywords: {keywords}")

    # Test XMP metadata generation
    xmp_metadata = metadata.to_xmp_metadata()
    assert xmp_metadata is not None
    print("✅ XMP metadata generation successful!")


@pytest.mark.django_db
def test_pdf_integration(dossier_with_documents):
    """Test integration with actual PDF creation and reading."""
    print("\n=== Testing PDF Integration ===")

    # Get the semantic document from the fixture
    semantic_document = dossier_with_documents["semantic_documents"][0]

    # Create a simple PDF
    writer = PdfWriter()

    # Add a blank page (required for valid PDF)
    packet = BytesIO()
    c = canvas.Canvas(packet)
    c.drawString(100, 750, "Test Document")
    c.save()

    packet.seek(0)
    page_reader = PdfReader(packet)
    writer.add_page(page_reader.pages[0])

    # Add enhanced metadata using real semantic document
    add_metadata_docinfo_to_pdfwriter(writer, semantic_document)

    # Write to temporary file and read back - using context manager for automatic cleanup
    with tempfile.NamedTemporaryFile(suffix=".pdf", delete=True) as tmp_file:
        writer.write(tmp_file)
        tmp_file.flush()  # Ensure data is written to disk

        # Read back and verify
        with open(tmp_file.name, "rb") as f:
            reader = PdfReader(f)
            metadata = read_metadata_docinfo_from_pdf(reader)

        print("Read metadata from PDF:")
        print(f"  Standard metadata: {metadata['standard_metadata']}")
        print(f"  Prefixed keywords metadata: {metadata['prefixed_keywords_metadata']}")
        print(f"  XMP metadata available: {metadata['xmp_metadata'] is not None}")

        # Verify prefixed keywords data
        if metadata["prefixed_keywords_metadata"]:
            prefixed_keywords = metadata["prefixed_keywords_metadata"]
            assert (
                prefixed_keywords["document_category_key"]
                == semantic_document.document_category.name
            )
            print("✅ Prefixed keywords metadata correctly written and read!")
            print(
                f"✅ Document title in PDF: {metadata['standard_metadata'].get('/Title', 'N/A')}"
            )

        # Verify XMP metadata content
        if metadata["xmp_metadata"]:
            xmp_meta = metadata["xmp_metadata"]
            # Check key XMP fields
            assert xmp_meta["dc_creator"] == PDF_METADATA_DOCINFO_CREATOR
            assert (
                xmp_meta["hypodossier_documentcategorykey"]
                == semantic_document.document_category.name
            )
            assert xmp_meta["hypodossier_version"] == "1.0.0"
            assert "hypodossier_uuid" in xmp_meta
            print("✅ XMP metadata content verified!")
        else:
            assert False, "XMP metadata should be available"

        # File will be automatically cleaned up when exiting the context manager


@pytest.mark.django_db
def test_suffix_comma_stripping():
    """Test that commas are stripped from title_suffix to prevent keyword parsing issues."""
    print("\n=== Testing Suffix Comma Stripping ===")

    # Test PdfMetadataDocinfo field validator
    metadata = PdfMetadataDocinfo(
        uuid="********-1234-5678-9012-********9abc",
        title="Test Document",
        document_category_key="TEST_KEY",
        document_category_name="Test Category",
        document_category_title_de="Test DE",
        document_category_title_en="Test EN",
        document_category_title_fr="Test FR",
        document_category_title_it="Test IT",
        title_suffix="suffix, with, commas",
    )

    # Verify commas are stripped from title_suffix
    assert metadata.title_suffix == "suffix with commas"
    print(
        f"✅ Original suffix 'suffix, with, commas' cleaned to: '{metadata.title_suffix}'"
    )

    # Test prefixed keywords don't contain problematic commas
    keywords = metadata.create_prefixed_keywords()
    suffix_keyword = next((k for k in keywords if k.startswith("suffix:")), None)
    assert suffix_keyword == "suffix:suffix with commas"
    print(f"✅ Suffix keyword: {suffix_keyword}")

    # Test that keywords can be properly parsed back
    keywords_string = ", ".join(keywords)
    parsed_keywords = ParsedKeywords.from_keywords_string(keywords_string)
    assert parsed_keywords.structured.get("suffix") == "suffix with commas"
    print(f"✅ Keywords string: {keywords_string}")
    print(f"✅ Parsed suffix: {parsed_keywords.structured.get('suffix')}")

    # Test with None suffix
    metadata_no_suffix = PdfMetadataDocinfo(
        uuid="********-1234-5678-9012-********9abc",
        title="Test Document",
        document_category_key="TEST_KEY",
        document_category_name="Test Category",
        document_category_title_de="Test DE",
        document_category_title_en="Test EN",
        document_category_title_fr="Test FR",
        document_category_title_it="Test IT",
        title_suffix=None,
    )
    assert metadata_no_suffix.title_suffix is None
    print("✅ None suffix handled correctly")


@pytest.mark.django_db
def test_edge_cases(dossier_with_documents):
    """Test edge cases for metadata generation with real data."""
    print("\n=== Testing Edge Cases ===")

    # Get the semantic document from the fixture
    semantic_document = dossier_with_documents["semantic_documents"][0]

    # Test creating metadata from real semantic document
    metadata = create_pdf_metadata_docinfo(semantic_document)

    print("✅ Real document metadata created successfully")
    print(f"✅ Title: {metadata.title}")
    print(f"✅ Category: {metadata.document_category_name}")
    print(f"✅ UUID: {metadata.uuid}")

    # Test that all required fields are present
    assert metadata.uuid is not None
    assert metadata.title is not None
    assert metadata.document_category_key is not None
    assert metadata.document_category_name is not None

    # Test that we can create both types of metadata
    pdf_info = create_document_info_properties_with_prefixed_keywords(metadata)
    assert pdf_info is not None
    assert "/Title" in pdf_info
    print(f"✅ PDF info created: {pdf_info.get('/Title', 'N/A')}")

    # Test XMP metadata
    xmp_metadata = metadata.to_xmp_metadata()
    assert xmp_metadata is not None
    print("✅ XMP metadata created successfully")

    # Test that metadata can be parsed back
    parsed = parse_document_info_properties_with_prefixed_keywords(pdf_info)
    assert parsed is not None
    assert "document_category_key" in parsed
    print(
        f"✅ Metadata parsing successful: {parsed.get('document_category_key', 'N/A')}"
    )
