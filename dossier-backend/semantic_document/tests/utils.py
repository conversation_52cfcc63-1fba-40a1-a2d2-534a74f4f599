from io import BytesIO

import pypdf

from semantic_document.pdf_metadata_docinfo import (
    read_metadata_docinfo_from_pdf,
    has_hypodossier_metadata_docinfo_from_reader,
    PDF_METADATA_DOCINFO_CREATOR,
    PDF_METADATA_DOCINFO_PRODUCER,
)


def assert_pdf_has_hypodossier_metadata(
    pdf_content: bytes, semantic_document, expected_title_suffix: str = None
):
    """
    Helper function to validate that a PDF contains proper Hypodossier metadata.

    Args:
        pdf_content: PDF file content as bytes
        semantic_document: The semantic document that was exported
        expected_title_suffix: Expected title suffix (optional)
    """
    pdf_reader = pypdf.PdfReader(BytesIO(pdf_content))

    # Read and validate specific metadata fields
    metadata = read_metadata_docinfo_from_pdf(pdf_reader)

    # Validate standard metadata exists
    standard_metadata = metadata.get("standard_metadata", {})
    assert standard_metadata, "PDF should contain standard metadata"

    # Check if we have enhanced Hypodossier metadata
    has_metadata_docinfo = has_hypodossier_metadata_docinfo_from_reader(pdf_reader)

    if has_metadata_docinfo:
        # Validate enhanced metadata fields
        assert (
            standard_metadata.get("/Creator") == PDF_METADATA_DOCINFO_CREATOR
        ), f"Expected Creator to be '{PDF_METADATA_DOCINFO_CREATOR}', got {standard_metadata.get('/Creator')}"
        assert (
            standard_metadata.get("/Producer") == PDF_METADATA_DOCINFO_PRODUCER
        ), f"Expected Producer to be '{PDF_METADATA_DOCINFO_PRODUCER}', got {standard_metadata.get('/Producer')}"

        # Validate prefixed keywords metadata
        prefixed_metadata = metadata.get("prefixed_keywords_metadata", {})
        if prefixed_metadata:
            assert (
                prefixed_metadata.get("document_category_key")
                == semantic_document.document_category.name
            ), f"Expected document category {semantic_document.document_category.name}, got {prefixed_metadata.get('document_category_key')}"
            assert prefixed_metadata.get("uuid") == str(
                semantic_document.uuid
            ), f"Expected UUID {semantic_document.uuid}, got {prefixed_metadata.get('uuid')}"

            if expected_title_suffix:
                assert (
                    prefixed_metadata.get("title_suffix") == expected_title_suffix
                ), f"Expected title suffix '{expected_title_suffix}', got {prefixed_metadata.get('title_suffix')}"

        # Validate XMP metadata if present
        xmp_metadata = metadata.get("xmp_metadata", {})
        if xmp_metadata:
            assert (
                "hypodossier_documentcategorykey" in xmp_metadata
            ), "XMP metadata should contain document category"
            assert (
                "hypodossier_uuid" in xmp_metadata
            ), "XMP metadata should contain UUID"
    else:
        # For PDFs without docinfo metadata, just verify basic structure and log what we found
        print(
            f"PDF does not have Hypodossier metadata docinfo. Found standard metadata: {standard_metadata}"
        )
        # At minimum, verify the PDF has some metadata and is valid
        assert len(pdf_reader.pages) > 0, "PDF should have at least one page"
