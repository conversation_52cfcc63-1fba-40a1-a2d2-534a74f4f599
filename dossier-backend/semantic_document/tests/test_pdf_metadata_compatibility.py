"""
Tests to verify PDF metadata functions work with both schema objects and model instances.
This test specifically addresses the AttributeError and TypeError issues that were fixed.
"""

import pytest

from dossier.conftest import create_synthetic_dossier
from dossier.fakes import (
    load_initial_document_categories,
    add_some_fake_semantic_documents,
)
from dossier.models import DocumentCategory
from semantic_document.services_pdf import create_pdf_semantic_document_metadata
from semantic_document.pdf_metadata_docinfo import create_pdf_metadata_docinfo
from dossier.doc_cat_helpers import get_document_categories_by_name_for_account


@pytest.fixture
def synthetic_dossier():
    """Create a synthetic dossier for testing."""
    return create_synthetic_dossier()


@pytest.fixture
def dossier_with_documents(synthetic_dossier):
    """Create a dossier with document categories and semantic documents."""
    account = synthetic_dossier.account

    # Load document categories
    load_initial_document_categories(account=account)

    # Get a document category
    document_category = DocumentCategory.objects.filter(account=account).first()

    # Create semantic documents
    semantic_documents = add_some_fake_semantic_documents(
        synthetic_dossier,
        num_docs=2,  # Create 2 documents for testing different scenarios
        allow_empty_docs=False,
        valid_document_category_keys=[document_category.name],
        max_pages=5,
        min_num_pages=3,
        title_suffix="test suffix",  # Ensure we have a title_suffix to test
    )

    return {
        "dossier": synthetic_dossier,
        "account": account,
        "document_category": document_category,
        "semantic_documents": semantic_documents,
    }


@pytest.mark.django_db
class TestPdfMetadataCompatibility:
    """Test that PDF metadata functions work with both SemanticDocumentFullApiData and SemanticDocument objects."""

    def test_semantic_document_title_suffix_access(self, dossier_with_documents):
        """Test that SemanticDocument model instances have title_suffix attribute access working correctly."""
        # Get real semantic documents from the fixture
        semantic_documents = dossier_with_documents["semantic_documents"]
        semantic_document = semantic_documents[0]

        # Test the fixed attribute access pattern with real SemanticDocument
        def get_suffix_value(document):
            """The pattern used in the fixed code."""
            if hasattr(document, "suffix"):
                return document.suffix
            elif hasattr(document, "title_suffix"):
                return document.title_suffix
            return None

        # Real SemanticDocument should have title_suffix, not suffix
        assert hasattr(semantic_document, "title_suffix")
        assert not hasattr(semantic_document, "suffix")

        # The function should work without AttributeError
        suffix_value = get_suffix_value(semantic_document)
        assert suffix_value == semantic_document.title_suffix

        # Test with a document that has no suffix
        semantic_document.title_suffix = None
        semantic_document.save()
        assert get_suffix_value(semantic_document) is None

        # Test with a document that has a suffix
        semantic_document.title_suffix = "renewal"
        semantic_document.save()
        assert get_suffix_value(semantic_document) == "renewal"

    def test_semantic_pages_related_manager_access(self, dossier_with_documents):
        """Test that semantic pages RelatedManager access patterns don't raise TypeError."""
        # Get real semantic documents from the fixture
        semantic_documents = dossier_with_documents["semantic_documents"]
        semantic_document = semantic_documents[0]

        # Test the fixed semantic pages access pattern with real RelatedManager
        def get_semantic_pages(document):
            """The pattern used in the fixed code."""
            if hasattr(document, "semantic_pages"):
                if hasattr(document.semantic_pages, "all"):
                    # Django RelatedManager - call .all() to get queryset
                    return document.semantic_pages.all()
                else:
                    # List from schema object
                    return document.semantic_pages
            return None

        # Real SemanticDocument should have semantic_pages as RelatedManager
        assert hasattr(semantic_document, "semantic_pages")
        assert hasattr(semantic_document.semantic_pages, "all")

        # This should work without TypeError (the original issue was len() on RelatedManager)
        semantic_pages = get_semantic_pages(semantic_document)
        assert semantic_pages is not None

        # Should be able to get length of the queryset (not the RelatedManager directly)
        pages_count = len(semantic_pages)
        assert pages_count >= 0  # Should have some pages from the fixture

        # Verify we can iterate over the pages
        for page in semantic_pages:
            assert hasattr(
                page, "number"
            )  # SemanticPage uses 'number', not 'page_number'
            assert hasattr(page, "semantic_document")

        # Test that direct access to RelatedManager would fail (the original bug)
        # This demonstrates what was fixed
        try:
            # This would have caused the original TypeError
            len(semantic_document.semantic_pages)  # This should fail
            assert False, "Expected TypeError when calling len() on RelatedManager"
        except TypeError as e:
            assert "object of type 'RelatedManager' has no len()" in str(e)

    def test_pdf_metadata_functions_with_real_semantic_document(
        self, dossier_with_documents
    ):
        """Test that both PDF metadata functions work correctly with real SemanticDocument instances."""
        # Get real semantic documents from the fixture
        semantic_documents = dossier_with_documents["semantic_documents"]
        semantic_document = semantic_documents[0]

        # Ensure the document has a title_suffix for testing
        semantic_document.title_suffix = "test renewal"
        semantic_document.save()

        # Test create_pdf_semantic_document_metadata function
        try:
            metadata_v1 = create_pdf_semantic_document_metadata(semantic_document)

            # Verify the metadata was created successfully
            assert metadata_v1 is not None
            assert metadata_v1.title is not None
            assert (
                metadata_v1.document_category_key
                == semantic_document.document_category.name
            )
            assert metadata_v1.title_suffix == "test renewal"
            assert metadata_v1.uuid == str(semantic_document.uuid)

        except AttributeError as e:
            if "'SemanticDocument' object has no attribute 'suffix'" in str(e):
                pytest.fail(
                    "Original AttributeError was not fixed in create_pdf_semantic_document_metadata"
                )
            raise
        except TypeError as e:
            if "object of type 'RelatedManager' has no len()" in str(e):
                pytest.fail(
                    "Original TypeError was not fixed in create_pdf_semantic_document_metadata"
                )
            raise

        # Test create_pdf_metadata_docinfo function
        try:
            metadata_v2 = create_pdf_metadata_docinfo(semantic_document)

            # Verify the metadata docinfo was created successfully
            assert metadata_v2 is not None
            assert metadata_v2.title is not None
            assert (
                metadata_v2.document_category_key
                == semantic_document.document_category.name
            )
            assert metadata_v2.title_suffix == "test renewal"
            assert metadata_v2.uuid == str(semantic_document.uuid)

            # Test that metadata can create prefixed keywords
            keywords = metadata_v2.create_prefixed_keywords()
            assert any(keyword.startswith("category:") for keyword in keywords)
            assert any(keyword.startswith("suffix:") for keyword in keywords)

        except AttributeError as e:
            if "'SemanticDocument' object has no attribute 'suffix'" in str(e):
                pytest.fail(
                    "Original AttributeError was not fixed in create_pdf_metadata_docinfo"
                )
            raise
        except TypeError as e:
            if "object of type 'RelatedManager' has no len()" in str(e):
                pytest.fail(
                    "Original TypeError was not fixed in create_pdf_metadata_docinfo"
                )
            raise

    def test_edge_cases_with_real_data(self, dossier_with_documents):
        """Test edge cases for metadata generation with real semantic documents."""
        # Get real semantic documents from the fixture
        semantic_documents = dossier_with_documents["semantic_documents"]

        # Test with document that has no title_suffix
        semantic_document_no_suffix = semantic_documents[0]
        semantic_document_no_suffix.title_suffix = None
        semantic_document_no_suffix.save()

        # Both functions should handle None title_suffix gracefully
        metadata_v1 = create_pdf_semantic_document_metadata(semantic_document_no_suffix)
        assert metadata_v1.title_suffix is None

        metadata_v2 = create_pdf_metadata_docinfo(semantic_document_no_suffix)
        assert metadata_v2.title_suffix is None

    def test_account_specific_document_categories(self, dossier_with_documents):
        """Test that account-specific document categories work correctly."""
        # Get the semantic document and its account
        semantic_document = dossier_with_documents["semantic_documents"][0]
        account = semantic_document.dossier.account

        # Pre-fetch account-specific document categories
        document_categories = get_document_categories_by_name_for_account(account)

        # Test that the functions work with pre-fetched categories
        metadata_v1 = create_pdf_semantic_document_metadata(
            semantic_document, document_categories
        )
        assert metadata_v1 is not None
        assert (
            metadata_v1.document_category_key
            == semantic_document.document_category.name
        )

        metadata_v2 = create_pdf_metadata_docinfo(
            semantic_document, document_categories
        )
        assert metadata_v2 is not None
        assert (
            metadata_v2.document_category_key
            == semantic_document.document_category.name
        )

        # Verify that the document category comes from the correct account
        assert semantic_document.document_category.name in document_categories
        fetched_category = document_categories[semantic_document.document_category.name]
        assert fetched_category.account == account
