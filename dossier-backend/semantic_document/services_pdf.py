"""
PDF generation services for semantic documents with metadata support.
"""

import json
import tempfile
from io import BytesIO
from pathlib import Path
from typing import Optional, Sequence, Callable, List, Union, Dict, Any

import pypdf
import requests
import structlog
from pydantic import BaseModel
from pypdf import PdfWriter, PdfReader
from pypdf.generic import (
    DictionaryObject,
    NameObject,
    ArrayObject,
    NumberObject,
    FloatObject,
)
from reportlab.lib.styles import ParagraphStyle
from reportlab.pdfgen import canvas
from reportlab.platypus.paragraph import Paragraph

from dossier.schemas import SemanticDocumentFullApiData
from dossier_zipper.helpers import rotate_pdf
from semantic_document.models import SemanticDocument
from semantic_document.schemas import SemanticDocumentMetadata
from semantic_document.pdf_metadata_docinfo import (
    PDF_METADATA_DOCINFO_ENABLED,
    add_metadata_docinfo_to_pdfwriter,
    convert_semantic_document_to_metadata,
)
from semantic_document.pdf_styles import (
    create_test_style,
    create_optimal_style,
    create_comment_style,
)
from semantic_document.schemas_page_annotations import (
    AnnotationType,
    UserAnnotationsSchema,
)

logger = structlog.get_logger()

# Version of Schema for the metadata in the JSON attachment
# This is not the same as the metadata in the PDF document itself
METADATA_JSON_SCHEMA_VERSION = "1.0.0"

# PDF Generation Constants
DEFAULT_HIGHLIGHT_OPACITY = 0.5
COMMENT_BOX_PADDING_RATIO = 0.002  # Padding as percentage of page width
COMMENT_BOX_FILL_OPACITY = 1.0  # Full opacity for comment background rectangles

# Font Size Optimization Constants
MIN_READABLE_FONT_SIZE = 2.0  # Minimum font size for readability (PDF can be zoomed)
MAX_FONT_HEIGHT_RATIO = 0.9  # Maximum font size as ratio of frame height
FALLBACK_MAX_FONT_SIZE = 18.0  # Fallback maximum font size in points
FONT_SIZE_PRECISION = 0.1  # Binary search precision for font size optimization
LEADING_MULTIPLIER = 1.2  # Leading multiplier for line spacing

# PDF Annotation Constants
ANNOTATION_FLAG_PRINT = 4  # PDF annotation flag for printing


class PdfSemanticDocumentMetadata(BaseModel):
    version: str
    uuid: str
    title: str
    document_category_key: str
    document_category_name: str
    document_category_title_de: str
    document_category_title_en: str
    document_category_title_fr: str
    document_category_title_it: str
    title_suffix: Optional[str] = None
    # filename: str
    # confidence: float
    # creator: str
    # producer: str


def create_document_info_properties(data: PdfSemanticDocumentMetadata):
    # Create full title with suffix if available
    full_title = data.title
    if data.title_suffix:
        full_title = f"{data.title} {data.title_suffix}"

    return {
        "/Title": full_title,
        "/Author": "",
        "/Subject": data.title,  # Base title without suffix
        "/DocumentCategoryKey": data.document_category_key,
        "/TitleSuffix": data.title_suffix,
        "/Creator": "HypoDossier AG",
        "/Producer": "Dossier Manager 1.0",
    }


def add_metadata_json_to_pdfwriter(
    writer: PdfWriter,
    document: Union[SemanticDocumentFullApiData, "SemanticDocument"],
    document_categories: Optional[Dict[str, Any]] = None,
):
    """
    Add JSON metadata attachment to PDF writer.

    LEGACY FUNCTION: This function maintains backward compatibility with Union type parameters.
    Internally, it converts to SemanticDocumentMetadata to eliminate Union type complexity.

    For new code, prefer using:
    1. convert_semantic_document_to_metadata() to get normalized metadata
    2. add_metadata_json_to_pdfwriter_from_metadata() for clean API

    Args:
        writer: PDF writer instance
        document: Semantic document data (schema object or model instance)
                 Union type handling is done internally via conversion
        document_categories: Optional pre-fetched document categories
    """
    # Convert to normalized metadata first - this handles all Union type complexity
    metadata = convert_semantic_document_to_metadata(document, document_categories)

    # Use the clean implementation
    add_metadata_json_to_pdfwriter_from_metadata(writer, metadata)


def add_metadata_json_to_pdfwriter_from_metadata(
    writer: PdfWriter,
    metadata: SemanticDocumentMetadata,
):
    """
    Add JSON metadata attachment to PDF writer from SemanticDocumentMetadata.

    This is the clean, new API that works with the normalized metadata schema.
    It eliminates Union type complexity and provides better type safety.

    Args:
        writer: PDF writer instance
        metadata: Normalized semantic document metadata
    """
    # Create metadata using clean API
    pdf_metadata = create_pdf_semantic_document_metadata_from_metadata(metadata)

    with tempfile.TemporaryDirectory() as temp_dir:
        metafile_path = Path(temp_dir) / "HypoDossierData.json"
        with open(metafile_path, "w") as f:
            j = json.dumps(pdf_metadata.model_dump(), indent=4)
            f.write(j)

        with open(metafile_path, "rb") as f:
            txt_data = f.read()
            writer.add_attachment(metafile_path.name, txt_data)


def create_pdf_semantic_document_metadata(
    document: Union[SemanticDocumentFullApiData, "SemanticDocument"],
    document_categories: Optional[Dict[str, Any]] = None,
):
    """
    Create PDF semantic document metadata.

    LEGACY FUNCTION: This function maintains backward compatibility with existing code
    that passes Union[SemanticDocumentFullApiData, "SemanticDocument"].

    For new code, prefer using:
    1. convert_semantic_document_to_metadata() to get normalized metadata
    2. create_pdf_semantic_document_metadata_from_metadata() for clean API

    This approach eliminates Union type complexity and is more maintainable.

    Args:
        document: Semantic document data (schema object or model instance)
        document_categories: Optional pre-fetched document categories to avoid N+1 queries.
                           Should be account-specific to ensure correct titles.

    Returns:
        PdfSemanticDocumentMetadata: Metadata object for the semantic document
    """
    # Convert to normalized metadata first - this handles all the Union type complexity
    metadata = convert_semantic_document_to_metadata(document, document_categories)

    return PdfSemanticDocumentMetadata(
        version=METADATA_JSON_SCHEMA_VERSION,
        title=metadata.title,
        document_category_key=metadata.document_category_key,
        document_category_name=metadata.document_category_name,
        document_category_title_de=metadata.document_category_title_de,
        document_category_title_en=metadata.document_category_title_en,
        document_category_title_fr=metadata.document_category_title_fr,
        document_category_title_it=metadata.document_category_title_it,
        title_suffix=metadata.title_suffix,
        uuid=str(metadata.uuid),
    )


def create_pdf_semantic_document_metadata_from_metadata(
    metadata: SemanticDocumentMetadata,
) -> PdfSemanticDocumentMetadata:
    """
    Create PDF semantic document metadata from SemanticDocumentMetadata.

    This is the clean, new API that works with the normalized metadata schema.
    It eliminates the need for complex Union type handling and hasattr() checks
    that were present in the original create_pdf_semantic_document_metadata() function.

    Benefits of this approach:
    - No Union type complexity - works with single, well-defined type
    - All required fields are guaranteed to be present and normalized
    - No need for hasattr() checks or conditional logic
    - Better type safety and IDE support
    - More efficient - no repeated document category queries

    Args:
        metadata: Normalized semantic document metadata containing all required fields
                 with consistent naming and pre-computed localized titles

    Returns:
        PdfSemanticDocumentMetadata: Object containing metadata for JSON attachment
                                   to PDF files, used by the semantic document processing pipeline

    Example:
        # Convert document to metadata first
        metadata = convert_semantic_document_to_metadata(document)

        # Use clean API
        pdf_metadata = create_pdf_semantic_document_metadata_from_metadata(metadata)

        # Much cleaner than the old approach:
        # pdf_metadata = create_pdf_semantic_document_metadata(document, categories)
    """
    return PdfSemanticDocumentMetadata(
        version=METADATA_JSON_SCHEMA_VERSION,
        title=metadata.title,
        document_category_key=metadata.document_category_key,
        document_category_name=metadata.document_category_name,
        document_category_title_de=metadata.document_category_title_de,
        document_category_title_en=metadata.document_category_title_en,
        document_category_title_fr=metadata.document_category_title_fr,
        document_category_title_it=metadata.document_category_title_it,
        title_suffix=metadata.title_suffix,
        uuid=str(metadata.uuid),
    )


def hex_to_rgb_float(hex_color: str):
    """
    Convert a hexadecimal color string to RGB float tuple (R, G, B).
    Each value will be between 0 and 1.
    """
    hex_color = hex_color.lstrip("#")
    lv = len(hex_color)
    if lv != 6:
        raise ValueError(
            "Incorrect hex color format. Use 6-digit format without '#' prefix."
        )
    return tuple(int(hex_color[i : i + 2], 16) / 255.0 for i in range(0, lv, 2))


def normalize_newlines(text: str) -> str:
    """
    Normalize different types of newlines to a consistent format.

    Args:
        text: The text to normalize

    Returns:
        str: Text with normalized newlines
    """
    if not text:
        return ""
    return text.replace("\r\n", "\n").replace("\r", "\n").strip()


def prepare_text_for_reportlab(text: str) -> str:
    if not text:
        return ""
    else:
        t1 = normalize_newlines(text)
        t2 = t1.replace("\n", "<br/>")
        return t2


def check_text_overflow(
    text: str, frame_width: float, frame_height: float, style: ParagraphStyle
) -> bool:
    """
    Check if text will overflow the given frame dimensions.

    Args:
        text: The text to check
        frame_width: Width of the frame in points
        frame_height: Height of the frame in points
        style: ParagraphStyle to use for rendering

    Returns:
        bool: True if text overflows, False otherwise
    """
    if not text:
        return False

    # Normalize newlines before processing
    text = prepare_text_for_reportlab(text)

    # Ensure style has proportional leading if not already set
    if not hasattr(style, "leading") or style.leading is None:
        style.leading = style.fontSize * 1.2

    # Create paragraph with the text
    p = Paragraph(text, style)

    # Get the space required by the paragraph
    needed_width, needed_height = p.wrap(frame_width, frame_height)

    # If needed_height is zero, text couldn't be wrapped to fit width
    if needed_height == 0:
        return True

    # Check if text needs more height than available
    if needed_height > frame_height:
        return True

    return False


def find_optimal_font_size(
    text: str, frame_width: float, frame_height: float, base_style: ParagraphStyle
) -> float:
    """
    Find the optimal font size that allows text to fit within the given frame dimensions.
    Uses binary search to efficiently find the largest font size that fits.

    Args:
        text: The text to fit
        frame_width: Width of the frame in points
        frame_height: Height of the frame in points
        base_style: Base ParagraphStyle to use for rendering

    Returns:
        float: Optimal font size in points
    """
    if not text:
        return base_style.fontSize

    # Normalize newlines before processing
    text = prepare_text_for_reportlab(text)

    # Define search range for font sizes using constants
    min_font_size = MIN_READABLE_FONT_SIZE
    max_font_size = max(frame_height * MAX_FONT_HEIGHT_RATIO, FALLBACK_MAX_FONT_SIZE)

    # Default to minimum if nothing fits
    optimal_font_size = min_font_size

    # Binary search for the optimal font size
    while max_font_size - min_font_size > FONT_SIZE_PRECISION:
        mid_font_size = (min_font_size + max_font_size) / 2

        # Create a new style with the current font size and proportional leading
        test_style = create_test_style(base_style=base_style, font_size=mid_font_size)

        # Check if text fits with this font size
        p = Paragraph(text, test_style)
        needed_width, needed_height = p.wrap(frame_width, frame_height)

        if frame_height >= needed_height > 0 and needed_width <= frame_width:
            # This font size fits, try a larger one
            min_font_size = mid_font_size
            optimal_font_size = mid_font_size
        else:
            # This font size is too large, try a smaller one
            max_font_size = mid_font_size

    # Return the largest font size that fits
    return optimal_font_size


def _calculate_annotation_coordinates(
    annotation: UserAnnotationsSchema, page_width: float, page_height: float
) -> tuple[float, float, float, float]:
    """
    Calculate absolute coordinates for annotation from relative bbox values.

    Args:
        annotation: Annotation schema with relative bbox coordinates (0-1)
        page_width: Page width in points
        page_height: Page height in points

    Returns:
        Tuple of (left, bottom, right, top) coordinates in PDF coordinate system
    """
    # Scale bbox values to absolute coordinates
    abs_top = annotation.bbox_top * page_height
    abs_height = annotation.bbox_height * page_height
    abs_left = annotation.bbox_left * page_width
    abs_width = annotation.bbox_width * page_width

    # PDF coordinate system with bottom-left origin
    bottom = page_height - (abs_top + abs_height)
    top = page_height - abs_top
    left = abs_left
    right = abs_left + abs_width

    return left, bottom, right, top


def _create_highlight_annotation(
    annotation: UserAnnotationsSchema,
    left: float,
    bottom: float,
    right: float,
    top: float,
    highlight_opacity: float = DEFAULT_HIGHLIGHT_OPACITY,
) -> DictionaryObject:
    """
    Create a highlight annotation dictionary for PDF.

    Args:
        annotation: Annotation schema containing color and other properties
        left, bottom, right, top: Coordinates in PDF coordinate system
        highlight_opacity: Opacity level for the highlight (0-1)

    Returns:
        DictionaryObject: PDF highlight annotation dictionary
    """
    rgb_color = hex_to_rgb_float(annotation.hexcolor)

    highlight_dict = DictionaryObject()
    highlight_dict.update(
        {
            NameObject("/Type"): NameObject("/Annot"),
            NameObject("/Subtype"): NameObject("/Highlight"),
            NameObject("/F"): NumberObject(ANNOTATION_FLAG_PRINT),
            NameObject("/Rect"): ArrayObject(
                [
                    FloatObject(left),
                    FloatObject(bottom),
                    FloatObject(right),
                    FloatObject(top),
                ]
            ),
            NameObject("/QuadPoints"): ArrayObject(
                [
                    FloatObject(left),
                    FloatObject(top),
                    FloatObject(right),
                    FloatObject(top),
                    FloatObject(left),
                    FloatObject(bottom),
                    FloatObject(right),
                    FloatObject(bottom),
                ]
            ),
            NameObject("/C"): ArrayObject([FloatObject(c) for c in rgb_color]),
            NameObject("/CA"): FloatObject(highlight_opacity),
        }
    )

    return highlight_dict


def add_annotations_to_page(
    page,
    annotations: List[UserAnnotationsSchema],
    writer: pypdf.PdfWriter,
    highlight_opacity: float = DEFAULT_HIGHLIGHT_OPACITY,
):
    """
    Add annotations to a PDF page.

    Args:
        page: PDF page object
        annotations: List of annotation schemas to add
        writer: PDF writer instance
        highlight_opacity: Opacity level for highlights (0-1)
    """
    if not hasattr(page, "annotations"):
        page[NameObject("/Annots")] = ArrayObject()

    page_height = float(page.mediabox.height)
    page_width = float(page.mediabox.width)

    for annotation in annotations:
        left, bottom, right, top = _calculate_annotation_coordinates(
            annotation, page_width, page_height
        )

        if annotation.annotation_type == AnnotationType.HIGHLIGHT:
            _add_highlight_annotation(
                page, annotation, left, bottom, right, top, writer, highlight_opacity
            )
        elif annotation.annotation_type == AnnotationType.COMMENT:
            _add_comment_annotation(
                page, annotation, left, bottom, right, top, page_width, page_height
            )


def _add_highlight_annotation(
    page,
    annotation: UserAnnotationsSchema,
    left: float,
    bottom: float,
    right: float,
    top: float,
    writer: pypdf.PdfWriter,
    highlight_opacity: float,
):
    """
    Add a highlight annotation to the PDF page.

    Args:
        page: PDF page object
        annotation: Annotation schema
        left, bottom, right, top: Coordinates in PDF coordinate system
        writer: PDF writer instance
        highlight_opacity: Opacity level for the highlight
    """
    highlight_dict = _create_highlight_annotation(
        annotation, left, bottom, right, top, highlight_opacity
    )

    # Ensure page has annotations array
    if not isinstance(page.get("/Annots"), ArrayObject):
        page[NameObject("/Annots")] = ArrayObject()

    # Add the annotation to the writer and get a reference
    highlight_ref = writer._add_object(highlight_dict)
    page["/Annots"].append(highlight_ref)


def _add_comment_annotation(
    page,
    annotation: UserAnnotationsSchema,
    left: float,
    bottom: float,
    right: float,
    top: float,
    page_width: float,
    page_height: float,
):
    """
    Add a comment annotation to the PDF page.

    Args:
        page: PDF page object
        annotation: Annotation schema containing text and styling
        left, bottom, right, top: Coordinates in PDF coordinate system
        page_width, page_height: Page dimensions in points
    """
    # Calculate dimensions
    abs_width = right - left
    abs_height = top - bottom

    # Create a rectangle overlay with text using reportlab
    packet = BytesIO()

    # Set up text rendering style
    style = create_comment_style()

    # Calculate padding and frame dimensions
    padding = page_width * COMMENT_BOX_PADDING_RATIO
    frame_width = abs_width - (2 * padding)
    frame_height = abs_height - (2 * padding)

    # Check for text overflow and find optimal font size
    overflow = check_text_overflow(
        annotation.text or "", frame_width, frame_height, style
    )

    optimal_font_size = find_optimal_font_size(
        annotation.text or "", frame_width, frame_height, style
    )

    # Create optimized style
    style = create_optimal_style(base_style=style, font_size=optimal_font_size)

    # Log font size adjustment
    logger.info(
        "Using optimal font size for comment box",
        annotation_group_uuid=annotation.annotation_group_uuid,
        font_size=optimal_font_size,
        leading=optimal_font_size * LEADING_MULTIPLIER,
        box_width=abs_width,
        box_height=abs_height,
        overflow=overflow,
    )

    # Create and render the comment
    _render_comment_overlay(
        packet,
        annotation,
        style,
        left,
        bottom,
        top,
        abs_width,
        abs_height,
        padding,
        frame_width,
        page_width,
        page_height,
    )

    # Apply overlay to page
    packet.seek(0)
    overlay = PdfReader(packet)
    page.merge_page(overlay.pages[0])


def _render_comment_overlay(
    packet: BytesIO,
    annotation: UserAnnotationsSchema,
    style: ParagraphStyle,
    left: float,
    bottom: float,
    top: float,
    abs_width: float,
    abs_height: float,
    padding: float,
    frame_width: float,
    page_width: float,
    page_height: float,
):
    """
    Render the comment text overlay on a canvas.

    Args:
        packet: BytesIO buffer for the canvas
        annotation: Annotation schema containing text and color
        style: Paragraph style for text rendering
        left, bottom, top: Position coordinates
        abs_width, abs_height: Box dimensions
        padding: Padding around text
        frame_width: Available width for text
        page_width, page_height: Page dimensions
    """
    # Prepare text and create paragraph
    text = prepare_text_for_reportlab(annotation.text)
    p = Paragraph(text, style)

    # Create canvas and draw background rectangle
    c = canvas.Canvas(packet, pagesize=(page_width, page_height))
    rgb_color = hex_to_rgb_float(annotation.hexcolor)
    c.setFillColorRGB(*rgb_color)
    c.setFillAlpha(COMMENT_BOX_FILL_OPACITY)
    c.rect(left, bottom, abs_width, abs_height, fill=1, stroke=0)

    # Determine paragraph wrapping and draw text
    actual_paragraph_width, actual_paragraph_height = p.wrapOn(
        c, frame_width, page_height
    )

    # Position text within the comment box
    text_x = left + padding
    text_y = top - actual_paragraph_height - padding
    p.drawOn(c, text_x, text_y)
    c.save()


def _process_pdf_page(
    page,
    get_pdf_url_func: Callable,
    get_annotations_func: Optional[Callable],
    writer: pypdf.PdfWriter,
) -> None:
    """
    Process a single page for PDF generation.

    Args:
        page: Page object to process
        get_pdf_url_func: Function to get PDF URL for the page
        get_annotations_func: Optional function to get annotations for the page
        writer: PDF writer instance
    """
    # Get PDF content for the page
    pdf_page_url = get_pdf_url_func(page)
    response = requests.get(pdf_page_url)
    response.raise_for_status()  # Raise exception for HTTP errors

    # Read PDF page
    pdf_reader = pypdf.PdfReader(BytesIO(response.content))
    pdf_page = pdf_reader.pages[0]

    # Apply rotation if needed
    rotation_angle = getattr(page, "rotation_angle", 0)
    rotated_page = rotate_pdf(pdf_page, rotation_angle)

    # Add annotations if available
    if get_annotations_func:
        annotations = get_annotations_func(page)
        if annotations and len(annotations) > 0:
            add_annotations_to_page(rotated_page, annotations, writer)

    # Add the processed page to the writer
    writer.add_page(rotated_page)


def create_semantic_document_pdf(
    *,
    semantic_document,
    pages: Sequence,
    get_pdf_url_func: Callable,
    get_annotations_func: Optional[Callable] = None,
    add_metadata_json: bool = False,
    add_metadata_docinfo: bool = PDF_METADATA_DOCINFO_ENABLED,
    writer: Optional[pypdf.PdfWriter] = None,
    dossier_language: Optional[str] = None,
    document_categories: Optional[Dict[str, Any]] = None,
) -> pypdf.PdfWriter:
    """
    Create a PDF from semantic document pages with optional annotations and metadata.

    Args:
        semantic_document: Semantic document instance
        pages: Sequence of pages to include in the PDF
        get_pdf_url_func: Function to get PDF URL for each page
        get_annotations_func: Optional function to get annotations for each page
        add_metadata_json: Whether to add JSON metadata attachment
        add_metadata_docinfo: Whether to add document info metadata
        writer: Optional existing PDF writer (creates new one if None)
        dossier_language: Language for metadata localization
        document_categories: Pre-fetched document categories for efficiency

    Returns:
        pypdf.PdfWriter: PDF writer with all pages and metadata
    """
    if writer is None:
        writer = pypdf.PdfWriter()

    # Process each page
    for page in pages:
        _process_pdf_page(page, get_pdf_url_func, get_annotations_func, writer)

    # Add metadata if requested
    if add_metadata_json:
        add_metadata_json_to_pdfwriter(writer, semantic_document, document_categories)

    if add_metadata_docinfo:
        add_metadata_docinfo_to_pdfwriter(
            writer,
            semantic_document,
            document_categories=document_categories,
            dossier_language=dossier_language,
        )

    # Add document outline
    filename = getattr(semantic_document, "filename", None) or semantic_document.title
    writer.add_outline_item(filename, 0)

    return writer
