import shutil
from collections import OrderedDict
from pathlib import Path
from tempfile import SpooledTemporaryFile
from typing import List, Dict, Optional, Tuple, Union
from datetime import datetime

from uuid import uuid4, UUID
from zipfile import ZipFile

import structlog
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, AbstractUser
from django.core.files import File
from django.db import transaction
from django.db.models import QuerySet, F, Q
from django.db.models.functions import Collate
from django.db.models.functions.text import Lower
from django.utils import timezone
import jwt
from pydantic import BaseModel, TypeAdapter

from bekb.bekb_instance_type import (
    get_bekb_instance_type,
    has_collaterals,
    is_bekb_fipla_account,
)
from bekb.schemas import schemas
from bekb.collaterals import (
    load_document_category2ekd_mappings,
    get_collateral_requirements_satisfaction_options_for_account,
    collateral_assignment_status,
    CollateralAssigmentStatus,
    get_collaterals_by_businesscase,
)
from bekb.data import DATA_PATH
from bekb.models import (
    Partner,
    BEKBDossierProperties,
    BusinessCase,
    CollateralAssignment,
    BEKBDossierExport,
    Attribute,
)
from bekb.schemas.schemas_fipla import DossierCreateJWTFipla
from bekb.schemas.schemas_services import (
    DossierValidationType,
    DocumentKeys,
    IndexFileEntry,
    IndexFileHeader,
)
from core.schema import Result, Error
from core.temporary_path import temporary_path
from dossier import services
from dossier.helpers_v2 import prepare_semantic_dossier
from dossier.models import (
    DossierUser,
    DossierRole,
    UserInvolvement,
    Account,
    DossierFile,
    Dossier,
    DocumentCategory,
)
from dossier.schemas import SemanticDossier
from dossier.services import change_dossier_work_status, is_pers
from dossier.helpers import create_export_filename
from dossier_zipper.packer import package_documents, PackerException
from semantic_document.helpers import fix_document_page_numbering
from semantic_document.models import SemanticDocument, SemanticPage
from semantic_document.services import copy_move_semantic_pages_from_queryset
from statemgmt.models import Status
from bekb.constants import archive
from statemgmt.services import check_work_status_transition_consistency


logger = structlog.get_logger()

EKD_WITH_DOCUMENT_PARKEYS = [
    "EKD51",
    "EKD53",
    "EKD54",
    "EKD55",
    "EKD56",
    "EKD57",
    "EKD58",
    "EKD59",
    "EKD60",
    "EKD61",
    "EKD62",
    "EKD63",
    "EKD64",
    "EKD65",
    "EKD66",
    "EKD67",
    "EKD68",
    "EKD73",
    "EKD74",
    "EKD75",
    "EKD81",
    "EKD82",
    "EKD83",
    "EKD84",
    "EKD85",
    "EKD86",
    "EKD87",
    "EKD88",
    "EKD89",
    "EKD95",
    "EKD96",
    "EKD97",
    "EKD100",
    "EKD104",
    "EKD105",
    "EKD106",
    "EKD107",
    "EKD112",
    "EKD113",
    "EKD114",
    "EKD115",
    "EKD117",
    "EKD119",
    "EKD120",
    "EKD121",
    "EKD122",
    "EKD123",
    "EKD124",
    "EKD130",
    "EKD131",
    "EKD132",
    "EKD133",
]

# change to get_valid_ready_for_export_status_keys(instance_type: BekbInstanceType)
VALID_READY_FOR_EXPORT_STATUS_KEYS_MORTGAGE = [
    "READY_FOR_EXPORT_DEAL",
    "READY_FOR_EXPORT_FICO",
    "READY_FOR_EXPORT_NO_DEAL",
]

VALID_READY_FOR_EXPORT_STATUS_KEYS_FIPLA = ["READY_FOR_EXPORT_FIPLA"]


# Moved to top of file for easier reading
class SemanticDocumentExportFile(BaseModel):
    filename: str
    pages: int


def set_pers(account, user, pers):
    pers_group, _ = Group.objects.get_or_create(name=f"BEKB/{account.key}/PERS")
    if pers:
        user.groups.add(pers_group)
    else:
        user.groups.remove(pers_group)


def assign_businesscase_to_dossier(
    bekb_dossier: BEKBDossierProperties, businesscase: BusinessCase
):
    bekb_dossier.business_case = businesscase
    bekb_dossier.save()


def create_bekb_dossier(
    account: Account,
    dossier_create: Union[schemas.DossierCreateJWTMortgage, DossierCreateJWTFipla],
    use_fico: bool,
) -> BEKBDossierProperties:
    # What are the custom attributes for this account? Just BEKBDossierProperties?
    # Lines 147 - 150 needed for SwissFex
    current_dossier_user = update_or_create_user(dossier_create.current_user, account)

    new_dossier = services.create_dossier(
        account,
        dossier_create.dossier_name,
        language=dossier_create.language,
        owner=current_dossier_user.user,
    )
    new_dossier.save()

    if use_fico:
        user = dossier_create.fico
        # FICO is BEKB only
        update_fico(account, new_dossier, user)

    services.change_assignee(current_dossier_user, new_dossier)

    partner_partner = None
    if (
        isinstance(dossier_create, schemas.DossierCreateJWTMortgage)
        and dossier_create.partner_partner is not None
    ):
        partner_partner, _ = Partner.objects.update_or_create(
            dossier_create.partner_partner.model_dump(),
            parkey=dossier_create.partner_partner.parkey,
            account=account,
        )

    business_partner = dossier_create.business_partner
    data = business_partner.model_dump()
    business_partner, _ = Partner.objects.update_or_create(
        data, parkey=business_partner.parkey, account=account
    )

    # Its a wrapper around the dossier
    new_bekb_dossier = BEKBDossierProperties.objects.create(
        account=account,
        dossier=new_dossier,
        partner_partner=partner_partner,
        business_partner=business_partner,
        pers=business_partner.pers,
    )
    return new_bekb_dossier


# Fico - Financial Coach, means sales person
def update_fico(account: Account, dossier: Dossier, user: schemas.User):
    fico_role, _ = get_fico_role(account)
    fico_dossier_user = update_or_create_user(user, account)
    UserInvolvement.objects.update_or_create(
        dict(user=fico_dossier_user), dossier=dossier, role=fico_role
    )


def get_fico_role(account):
    return DossierRole.objects.get_or_create(
        defaults=dict(name_de="FICO", name_fr="FICO", name_en="FICO", name_it="FICO"),
        key="FICO",
        show_separate_filter=True,
        account=account,
    )


def update_or_create_user(user_data: schemas.User, account: Account):
    User: AbstractUser = get_user_model()
    data = dict(
        last_name=user_data.name, email=user_data.username, is_active=user_data.active
    )
    if user_data.firstname is not None:
        data["first_name"] = user_data.firstname

    user, _ = User.objects.update_or_create(data, username=user_data.username)

    is_pers = user_data.pers

    set_pers(account, user, is_pers)

    dossier_user, _ = DossierUser.objects.get_or_create(account=account, user=user)
    return dossier_user


def create_or_update_partner(
    account: Account, parkey, name, firstname
) -> Tuple[Partner, bool]:
    """
    Creates or updates a partner.

    Args:
        account (Account): The account to which the partner belongs.
        parkey: The partner's parkey.
        name: The partner's name.
        firstname: The partner's first name.

    Returns:
        Tuple[Partner, bool]: The partner and a boolean indicating whether it was created (True)
        or updated (False).
    """
    property_partner, created = Partner.objects.update_or_create(
        dict(name=name, firstname=firstname), parkey=parkey, account=account
    )
    return property_partner, created


def sort_business_cases(
    business_cases: List[schemas.BusinessCase],
) -> List[schemas.BusinessCase]:
    """
    Sorts business cases based on their business parkey and business number.

    Args:
        business_cases (List[schemas.BusinessCase]): List of business cases to sort.

    Returns:
        List[schemas.BusinessCase]: Sorted list of business cases.
    """
    business_cases = sorted(
        business_cases, key=lambda x: (x.business_parkey, x.business_number)
    )
    return business_cases


def create_bekb_export_file(
    bekb_dossier: BEKBDossierProperties, is_bekb_fipla: bool
) -> bool:
    """
    Checks if all documents in a dossier have required EKD numbers.

    Args:
        is_bekb_fipla (bool): True if the export is for BEKB FIPLA, False otherwise.
        bekb_dossier (BEKBDossierProperties): The dossier to check.

    Returns:
        bool: True if all documents have required EKD numbers, False otherwise.
    """
    creation = timezone.now()

    assert bekb_dossier.dossier.work_status.key.startswith("READY_FOR_EXPORT")

    if bekb_dossier.dossier.work_status.key in [
        "READY_FOR_EXPORT_DEAL",
        "READY_FOR_EXPORT_FICO",
    ]:
        assert bekb_dossier.business_partner
        assert bekb_dossier.partner_partner
        assert bekb_dossier.business_case
        assert all_document_have_required_ekd_nr(bekb_dossier) is True
    elif bekb_dossier.dossier.work_status.key in ["READY_FOR_EXPORT_NO_DEAL"]:
        assert bekb_dossier.business_partner
    elif bekb_dossier.dossier.work_status.key in ["READY_FOR_EXPORT_FIPLA"]:
        assert bekb_dossier.business_partner
        assert not bekb_dossier.partner_partner
        assert not bekb_dossier.business_case
        assert all_document_have_required_ekd_nr(bekb_dossier) is True

    export_uuid = uuid4()
    creation_date = creation.strftime("%Y%m%d")
    archive_file_stem = (
        f"05_hypodossier_{bekb_dossier.account.key}_{creation_date}_{export_uuid}"
    )

    # TODO: make sure this is NOT used for bebkfipla
    if settings.ENABLE_BEKB_ARCHIVE_GROUPING_BY_DOCUMENT_CATEGORY:
        # If enabled group documents according to
        # https://gitlab.com/hypodossier/dossier-manager-frontend/-/issues/656
        semantic_document_map = calculate_bekb_dossier_semdoc_grouping(
            bekb_dossier.dossier
        )

        merge_bekb_semdoc_grouping_for_archive(
            semantic_document_map=semantic_document_map
        )

    with temporary_path() as temp_path:
        semantic_document_files = prepare_document_package_for_export(
            bekb_dossier, temp_path
        )

        batch_id = uuid4()
        index_file = create_index_file(
            batch_id,
            bekb_dossier,
            creation,
            semantic_document_files,
        )
        index_file_path = temp_path / f"{archive_file_stem}.ind"
        index_file_path.write_text(index_file)

        with SpooledTemporaryFile() as temp_file:
            with ZipFile(temp_file, "w") as zip:
                for path in temp_path.iterdir():
                    rel_path = path.relative_to(temp_path)
                    if path.is_file():
                        zip.write(path, rel_path)  # zipping the file

            temp_file.seek(0)

            dossier_file = DossierFile.objects.create(
                dossier=bekb_dossier.dossier,
                data=File(temp_file, name=f"{archive_file_stem}.zip"),
                bucket=bekb_dossier.dossier.bucket,
                created_at=creation_date,
            )

        return BEKBDossierExport.objects.create(
            uuid=export_uuid,
            account=bekb_dossier.account,
            dossier=bekb_dossier.dossier,
            file=dossier_file,
        )


def all_document_have_required_ekd_nr(bekb_dossier: BEKBDossierProperties):
    instance_type = get_bekb_instance_type(bekb_dossier.dossier.account.key)
    doc_key2ekd = load_document_category2ekd_mappings(instance_type)
    return all(
        [
            semantic_document.document_category.name in doc_key2ekd
            for semantic_document in bekb_dossier.dossier.semantic_documents.all()
        ]
    )


def prepare_document_package_for_export(
    bekb_dossier: BEKBDossierProperties, dest_path: Path
) -> Dict[UUID, SemanticDocumentExportFile]:
    """
    Prepares a document package for export.

    Args:
        bekb_dossier (BEKBDossierProperties): The dossier for which to prepare the document package.
        dest_path (Path): The destination path for the document package.

    Returns:
        Dict[UUID, SemanticDocumentExportFile]: A dictionary mapping UUIDs to semantic document export files.
    """

    semantic_dossier: SemanticDossier = prepare_semantic_dossier(
        bekb_dossier.dossier,
        show_soft_deleted=False,
        show_count_deleted_objects=False,
        hide_empty_semantic_documents=False,
        include_annotations=True,
    )
    document_package = package_documents(
        semantic_dossier, dest_path, add_metadata_json=False, add_metadata_docinfo=False
    )
    # for each semantic_document, there is a file required
    assert set(document_package.semantic_document_exports.keys()) == {
        sd.uuid for sd in bekb_dossier.dossier.semantic_documents.all()
    }
    semantic_document_files: Dict[UUID, SemanticDocumentExportFile] = {}
    for uuid, semantic_document in document_package.semantic_document_exports.items():
        export_filename = create_export_filename(semantic_document.filename)
        shutil.move(dest_path / semantic_document.filename, dest_path / export_filename)
        semantic_document_files[uuid] = SemanticDocumentExportFile(
            filename=export_filename, pages=semantic_document.pages
        )
    return semantic_document_files


def validate_dossier(
    bekb_dossier: BEKBDossierProperties,
) -> DossierValidationType:
    """Validates the dossier and returns validated information."""

    # Validate FIPLA status
    is_bekb_fipla = None

    # --- Validate the dossier status and determine whether dossier is fipla or mortgage ---
    if bekb_dossier.dossier.work_status.key in VALID_READY_FOR_EXPORT_STATUS_KEYS_FIPLA:
        is_bekb_fipla = True
        assert bekb_dossier.dossier.businesscase_type.key in [
            "INHERITANCE",
            "FINANCIAL_PLANNING",
        ]
    elif (
        bekb_dossier.dossier.work_status.key
        in VALID_READY_FOR_EXPORT_STATUS_KEYS_MORTGAGE
    ):
        is_bekb_fipla = False
    else:
        raise ValueError(
            f"Unexpected work status '{bekb_dossier.dossier.work_status.key}' during index file creation "
            f"for dossier {bekb_dossier.dossier.uuid}"
        )

    # Validate deal status
    # --- Set is_no_deal depending on status ---
    # Note partner_partner is currently required on BEKBDossierProperties model
    # so lots of this logic is not needed
    work_status_key = bekb_dossier.dossier.work_status.key
    if work_status_key in ["READY_FOR_EXPORT_DEAL", "READY_FOR_EXPORT_FICO"]:
        if not (
            bekb_dossier.business_partner
            and bekb_dossier.partner_partner
            and bekb_dossier.business_case
        ):
            raise ValueError("Dossier is missing required fields for deal export.")
        is_no_deal = False
    elif work_status_key == "READY_FOR_EXPORT_NO_DEAL":
        # For 'NO_DEAL' we only require business_partner
        if not bekb_dossier.business_partner:
            # This is already handled by business_partner being required by the model
            # but leave this logic here so its explicit
            raise ValueError("Missing business_partner for NO_DEAL export.")
        is_no_deal = True
    elif work_status_key == "READY_FOR_EXPORT_FIPLA":
        if not bekb_dossier.business_partner:
            # This is already handled by business_partner being required by the model
            # but leave this logic here so its explicit
            raise ValueError("Missing business_partner for FIPLA export.")
        is_no_deal = None
    else:
        raise ValueError(f"Unexpected work status '{work_status_key}'")

    # Determine parkey_p
    parkey_p = "0"
    if not is_bekb_fipla and not is_no_deal:
        parkey_p = bekb_dossier.partner_partner.parkey

    return DossierValidationType(
        is_bekb_fipla=is_bekb_fipla,
        is_no_deal=is_no_deal,
        parkey_g=bekb_dossier.business_partner.parkey,
        parkey_p=parkey_p,
        businesscase_number=(
            bekb_dossier.business_case.business_number
            if bekb_dossier.business_case
            else None
        ),
        pers=bekb_dossier.pers,
    )


def determine_document_keys(
    semantic_document: SemanticDocument,
    dossier_validation: DossierValidationType,
    bekb_dossier: BEKBDossierProperties,
    doc_key2ekd: Dict[str, str],
) -> DocumentKeys:
    """Determines all document-specific keys based on the semantic document and dossier validation."""

    if dossier_validation.is_no_deal:
        return DocumentKeys(ekd_nr="EKD81")

    if (
        dossier_validation.is_bekb_fipla
        and bekb_dossier.dossier.businesscase_type.key == "INHERITANCE"
    ):
        return DocumentKeys(ekd_nr="EKD134")

    # Normal case
    ekd_nr = doc_key2ekd[semantic_document.document_category.name]

    # Initialize with defaults
    keys = {
        "ekd_nr": ekd_nr,
        "parkey_l": "0",
        "parkey_d": "0",
        "parkey_dok": "0",
        "kre_key": "0",
        "lie_key": "0",
        "dec_key": "0",
    }

    collateral_assignment = CollateralAssignment.objects.filter(
        account=bekb_dossier.dossier.account,
        semantic_document=semantic_document,
    ).first()

    if collateral_assignment:
        keys["parkey_d"] = collateral_assignment.collateral.collateral_partner.parkey

        collateral_property = collateral_assignment.property
        if collateral_property is not None:
            keys["parkey_l"] = collateral_property.property_partner.parkey
            keys["lie_key"] = collateral_property.property_number

        if collateral_assignment.collateral.collateral_number:
            keys["dec_key"] = collateral_assignment.collateral.collateral_number

    if not dossier_validation.is_bekb_fipla:
        if ekd_nr in EKD_WITH_DOCUMENT_PARKEYS:
            keys["parkey_dok"] = bekb_dossier.partner_partner.parkey

        if dossier_validation.businesscase_number:
            keys["kre_key"] = dossier_validation.businesscase_number

    return DocumentKeys(**keys)


def create_index_entry(
    semantic_document: SemanticDocument,
    document_keys: DocumentKeys,
    dossier_validation: DossierValidationType,
    batch_id: UUID,
    creation: datetime,
    semantic_document_files: Dict[UUID, SemanticDocumentExportFile],
) -> str:
    """Creates a single index file entry."""

    entry = IndexFileEntry(
        scan_datum=semantic_document.created_at.strftime("%d.%m.%Y"),
        datum=creation.strftime("%d.%m.%Y"),
        batch_id=batch_id,
        filename=semantic_document_files[semantic_document.uuid].filename,
        perscode=int(dossier_validation.pers),
        seiten=semantic_document_files[semantic_document.uuid].pages,
        document_keys=document_keys,
    )

    # Format the entry
    parts = [
        # Top part
        f"GROUP_FIELD_NAME:SCANDATUM\nGROUP_FIELD_VALUE:{entry.scan_datum}",
        f"GROUP_FIELD_NAME:FORMULAR\nGROUP_FIELD_VALUE:{entry.document_keys.ekd_nr}",
        f"GROUP_FIELD_NAME:ORDNER\nGROUP_FIELD_VALUE:{entry.document_keys.ekd_nr}",
        f"GROUP_FIELD_NAME:FORMNR\nGROUP_FIELD_VALUE:{entry.document_keys.ekd_nr}",
        f"GROUP_FIELD_NAME:DATUM\nGROUP_FIELD_VALUE:{entry.datum}",
        f"GROUP_FIELD_NAME:BATCHID\nGROUP_FIELD_VALUE:{entry.batch_id}",
        f"GROUP_FIELD_NAME:PARKEY_G\nGROUP_FIELD_VALUE:{dossier_validation.parkey_g}",
    ]

    # Middle part (only for non-FIPLA)
    if not dossier_validation.is_bekb_fipla:
        parts.extend(
            [
                f"GROUP_FIELD_NAME:PARKEY_P\nGROUP_FIELD_VALUE:{dossier_validation.parkey_p}",
                f"GROUP_FIELD_NAME:PARKEY_D\nGROUP_FIELD_VALUE:{document_keys.parkey_d}",
                f"GROUP_FIELD_NAME:PARKEY_L\nGROUP_FIELD_VALUE:{document_keys.parkey_l}",
                f"GROUP_FIELD_NAME:PARKEY_DOK\nGROUP_FIELD_VALUE:{document_keys.parkey_dok}",
                f"GROUP_FIELD_NAME:KRE_KEY\nGROUP_FIELD_VALUE:{document_keys.kre_key}",
                f"GROUP_FIELD_NAME:LIE_KEY\nGROUP_FIELD_VALUE:{document_keys.lie_key}",
                f"GROUP_FIELD_NAME:DEC_KEY\nGROUP_FIELD_VALUE:{document_keys.dec_key}",
            ]
        )

    # Bottom part
    parts.extend(
        [
            f"GROUP_FIELD_NAME:PERSCODE\nGROUP_FIELD_VALUE:{entry.perscode}",
            f"GROUP_FIELD_NAME:KURZFORM\nGROUP_FIELD_VALUE:{entry.kurzform}",
            f"GROUP_FIELD_NAME:SEITEN\nGROUP_FIELD_VALUE:{entry.seiten}",
            f"GROUP_FIELD_NAME:FILENAME\nGROUP_FIELD_VALUE:{entry.filename}",
            "GROUP_OFFSET:0",
            "GROUP_LENGTH:0",
            f"GROUP_FILENAME:{entry.filename}",
        ]
    )

    return "\n".join(parts)


def create_index_file(
    batch_id: UUID,
    bekb_dossier: BEKBDossierProperties,
    creation: datetime,
    semantic_document_files: Dict[UUID, SemanticDocumentExportFile],
) -> str:
    """Creates an OnDemand index file needed for archive import."""

    # Validate dossier and get basic information
    dossier_validation = validate_dossier(bekb_dossier)

    # Load document mappings
    instance_type = get_bekb_instance_type(bekb_dossier.dossier.account.key)
    doc_key2ekd = load_document_category2ekd_mappings(instance_type)

    # Create header
    header = IndexFileHeader(creation_datetime=creation)

    # Create entries for each semantic document
    entries = []
    for semantic_document in bekb_dossier.dossier.semantic_documents.all():
        document_keys = determine_document_keys(
            semantic_document, dossier_validation, bekb_dossier, doc_key2ekd
        )

        entry = create_index_entry(
            semantic_document,
            document_keys,
            dossier_validation,
            batch_id,
            creation,
            semantic_document_files,
        )
        entries.append(entry)

    # Combine everything into final format
    return "\n".join([header.format()] + entries)


def map_user_to_schema(dossier_user: DossierUser) -> schemas.User:
    """
    Maps a DossierUser object to a User schema.

    Args:
        dossier_user (DossierUser): The user object to map.

    Returns:
        schemas.User: The mapped user schema.
    """
    return schemas.User(
        username=dossier_user.user.username,
        firstname=(
            dossier_user.user.first_name if dossier_user.user.first_name != "" else None
        ),
        name=dossier_user.user.last_name,
        pers=is_pers(dossier_user),
        active=dossier_user.user.is_active,
    )


def create_encoded_jwt(data: Dict, shared_secret=settings.BEKB_SHARED_SECRET) -> str:
    """
    Encodes a dictionary into a JWT.

    Args:
        data (Dict): The data to encode.
        shared_secret (str, optional): The shared secret to use for encoding. Defaults to settings.BEKB_SHARED_SECRET.

    Returns:
        str: The encoded JWT.
    """
    encoded_jwt = jwt.encode(data, shared_secret, algorithm="HS256")
    return encoded_jwt


def map_to_business_partner(business_partner: Partner):
    return schemas.BusinessPartner(**business_partner.__dict__)


def map_partner_to_schema(partner: Partner) -> schemas.Partner:
    """
    Maps a Partner object to a Partner schema.

    Args:
        partner (Partner): The partner object to map.

    Returns:
        schemas.Partner: The mapped partner schema.
    """
    return schemas.Partner(**partner.__dict__)


def get_fico(bekb_dossier: BEKBDossierProperties) -> AbstractUser:
    """
    Gets the FICO user for a given dossier.

    Args:
        bekb_dossier (BEKBDossierProperties): The dossier for which to get the FICO user.

    Returns:
        AbstractUser: The FICO user.
    """
    file_role, _ = get_fico_role(bekb_dossier.account)
    ui = UserInvolvement.objects.filter(
        role=file_role, dossier=bekb_dossier.dossier
    ).first()
    return ui.user


def list_actively_used_business_parkeys(account_name) -> List[str]:
    """
    Fetches business parkeys for all dossiers that are not expired for a given account.

    Args:
        account_name (str): Name of account for which the business parkeys are fetched.

    Returns:
        List[str]: List of business parkey as strings.
    """
    properties = (
        BEKBDossierProperties.objects.filter(dossier__account__key=account_name)
        .filter(dossier__expiry_date__gte=timezone.now())
        .values("business_partner__parkey")
        .distinct()
    )
    ret = [property["business_partner__parkey"] for property in properties]
    return ret


def process_ready_for_export_dossiers(
    account,
    dossier_uuids: List[UUID] = None,
    not_updated_since: timezone = None,
) -> List[Result[UUID]]:
    """
    Process dossiers that are ready for export (state == READY_FOR_EXPORT_XXX):
    - create export archive
    - change work status to EXPORT_ARCHIVE_AVAILABLE

    To run it on a single dossier:
    python manage.py process_ready_for_export_dossier now default 946c03bb-a075-4ad6-a802-b7f17524ed62

    Args:
        is_bekb_fipla (bool): True if the export is for BEKB FIPLA, False otherwise.
        account: The account to which the dossiers belong.
        dossier_uuids (List[UUID]): List of dossier UUIDs to process.
        not_updated_since (datetime): Process only the dossiers that haven't been updated since this datetime.

    Returns:
        List[Result[UUID]]: List of results for each processed dossier.
    """
    str_uuids = None
    if dossier_uuids:
        if len(dossier_uuids) < 10:
            str_uuids = str(dossier_uuids)
        else:
            str_uuids = len(dossier_uuids)

    logger.info(
        "start with process_ready_for_export_dossiers()...",
        account_key=account.key,
        len_dossier_uuids=str_uuids,
        not_updated_since=not_updated_since,
    )

    state_machine = account.active_work_status_state_machine
    assert state_machine is not None

    is_bekb_fipla = is_bekb_fipla_account(account.key)

    if is_bekb_fipla:
        ready_for_export_states = Status.objects.filter(
            state_machine=state_machine,
            key__in=VALID_READY_FOR_EXPORT_STATUS_KEYS_FIPLA,
        ).all()
    else:
        ready_for_export_states = Status.objects.filter(
            state_machine=state_machine,
            key__in=VALID_READY_FOR_EXPORT_STATUS_KEYS_MORTGAGE,
        ).all()

    bekb_dossier_qs = BEKBDossierProperties.objects.filter(
        account=account, dossier__work_status__in=ready_for_export_states
    )
    if not_updated_since:
        bekb_dossier_qs = bekb_dossier_qs.filter(updated_at__lt=not_updated_since)

    if dossier_uuids:
        bekb_dossier_qs = bekb_dossier_qs.filter(dossier__uuid__in=dossier_uuids)

    # Ensure that no expired dossiers are returned
    bekb_dossier_qs = bekb_dossier_qs.filter(dossier__expiry_date__gte=timezone.now())

    export_archive_available_state = Status.objects.get(
        state_machine=state_machine, key="EXPORT_ARCHIVE_AVAILABLE"
    )
    result: List[Result[UUID]] = []
    for bekb_dossier in bekb_dossier_qs.all():
        try:
            with transaction.atomic():
                logger.info(
                    f"creating dossier archive for bekb_dossier_uuid {bekb_dossier.uuid}, dossier_uuid {bekb_dossier.dossier.uuid}..."
                )
                current_dossier = BEKBDossierProperties.objects.get(
                    account=account, uuid=bekb_dossier.uuid
                )
                create_bekb_export_file(current_dossier, is_bekb_fipla=is_bekb_fipla)
                dossier = current_dossier.dossier

                change_dossier_work_status(
                    dossier,
                    dict(is_system=True, is_user=False),
                    export_archive_available_state,
                )
                dossier.save()
                result.append(Result[UUID](data=dossier.uuid))
        except PackerException as e:
            logger.exception(e)
            result.append(Result[UUID](error=Error(code=500, message=str(e))))
        except Exception as e:
            message = f"Could not create a dossier archive for bekb_dossier_uuid {bekb_dossier.uuid}, dossier_uuid {bekb_dossier.dossier.uuid}. e={e}"
            logger.exception(message)
            logger.exception(e)
            result.append(Result[UUID](error=Error(code=500, message=str(e))))
    return result


def all_collaterals_mapped(bekb_dossier: BEKBDossierProperties) -> bool:
    """
    Check if all collaterals are mapped for a given dossier.

    Args:
        bekb_dossier (BEKBDossierProperties): The dossier to check.

    Returns:
        bool: True if all collaterals are mapped, False otherwise.
    """
    account_key = bekb_dossier.account.key
    instance_type = get_bekb_instance_type(account_key)
    if not has_collaterals(instance_type):
        return True

    options = get_collateral_requirements_satisfaction_options_for_account(
        bekb_dossier.account
    )

    all_collaterals_valid = get_collaterals_by_businesscase(
        account=bekb_dossier.account,
        business_partner=bekb_dossier.business_partner,
        business_case=bekb_dossier.business_case,
    )

    cass = [
        collateral_assignment_status(all_collaterals_valid, document, options)
        for document in bekb_dossier.dossier.semantic_documents.all()
    ]

    print([cas.status for cas in cass])
    return all(
        [
            cas.status
            in [
                CollateralAssigmentStatus.Status.NO_COLLATERAL_REQUIRED,
                CollateralAssigmentStatus.Status.COLLATERAL_MAPPED,
            ]
            for cas in cass
        ]
    )


def get_attributes_as_dict(account: Account, entity: str) -> Dict[str, Attribute]:
    results = Attribute.objects.filter(entity=entity, account=account).all()
    results_dict = {}
    for c in results:
        results_dict[c.key] = c

    return results_dict


def load_bekb_attributes(account_key: str, path: str = None):
    if not path:
        path = str(DATA_PATH / "attributes/Attribute-2024-04-07.json")

    source_path = Path(path)

    assert source_path.exists()
    account = Account.objects.get(key=account_key)
    text = Path(source_path).read_text()
    attributes_dict = TypeAdapter(List[Dict[str, str]]).validate_json(text)

    assert attributes_dict
    num_created: int = 0
    for d in attributes_dict:
        entity = d["entity"]
        key = d["key"]
        del d["uuid"]
        del d["created_at"]
        del d["updated_at"]
        del d["account"]
        del d["entity"]
        del d["key"]
        _, created = Attribute.objects.update_or_create(
            d, entity=entity, key=key, account=account
        )
        if created:
            num_created += 1
        # else:
        #     logger.info(f'{entity} {key}')

    return len(attributes_dict), num_created


def calculate_bekb_dossier_semdoc_grouping(
    dossier: Dossier,
) -> dict[str, QuerySet[SemanticDocument]]:
    semantic_documents_qs = (
        SemanticDocument.objects.filter(dossier=dossier)
        .select_related(
            "document_category__account",
            "dossier",
            "collateralassignment__collateral",  # Accessing Collateral using the relation within CollateralAssignment
        )
        .filter(
            document_category__name__in=[
                item for sublist in archive.ALL_GROUP_TYPES.values() for item in sublist
            ]
        )
        # Sort by doc_cat_id, then title suffix case insensitive, with umlauts (ä after a, not after z)
        # and empty title_suffix first
        .order_by(
            "document_category__id",
            Collate(Lower("title_suffix"), "de-x-icu").asc(nulls_first=True),
        )
    )

    semdoc_map = OrderedDict()

    # Perform an additional filtering by group
    # We have 5 groups, so it's only an additional 5 queries
    for grouping_name, grouping_values in archive.ALL_GROUP_TYPES.items():
        sem_docs = semantic_documents_qs.filter(
            document_category__name__in=grouping_values
        )

        # Handle edge case 1: Do not combine if there is only 1 document in a group. If e.g. in PLAN_SITUATION / 'Plans'
        # there is only 1 semantic document to be combined, then do not combine it (leave it unchanged)
        if sem_docs.count() <= 1:
            continue

        # Handle edge case 2: change document_category_key_of_grouped_document in map if all documents in
        # list have same document category
        category_name = grouping_name

        # Count distinct document groups
        if sem_docs.values("document_category__name").distinct().count() == 1:
            # If all documents in a group have the same document_category_key then use this key instead of the
            # default key of the group. E.g. for grouping PLAN_SITUATION if you have only 3 documents
            # PLAN_CADASTER then the grouped document should be PLAN_CADASTER and not PLAN_SITUATION
            category_name = sem_docs.first().document_category.name

        semdoc_map[category_name] = sem_docs

    logger.info(
        "found semdoc_groupings",
        num_groups={len(semdoc_map)},
        groups={str(semdoc_map.keys())},
        dossier_uuid=dossier.uuid,
    )

    for doccat, list_of_semdocs in semdoc_map.items():
        logger.info(
            "semdoc_grouping details for doccat",
            doccat=doccat,
            list_of_semdocs=str(list_of_semdocs),
            dossier_uuid=dossier.uuid,
        )
    return semdoc_map


def merge_semantic_document_grouping_queryset_for_archive(
    document_category_name: str,
    semantic_documents_queryset: QuerySet[SemanticDocument],
    user: Optional[AbstractUser] = None,
):
    """
    Merges a group of SemanticDocument objects into the first semantic document.

    This function performs the following operations:
    1. Retrieves the first SemanticDocument from the queryset.
    2. If there are more than one document in the queryset, it moves all SemanticPage objects
       from the remaining SemanticDocument objects to the first document, and then deletes
       the remaining SemanticDocument objects.
    3. Updates the document_category of the first SemanticDocument to the provided
       document_category_name.
    4. Updates the title_suffix of the first SemanticDocument based on the language of its
       associated Dossier.
    5. Saves the first SemanticDocument with the updated changes.

    Args:
        document_category_name (str): The name of the DocumentCategory to which the merged
            document should be assigned.
        semantic_documents_queryset (QuerySet[SemanticDocument]): A queryset containing the
            SemanticDocument objects to be merged.
        user (Optional[AbstractUser], optional): The user performing the merge operation.
            If not provided, the owner of the first SemanticDocument's associated Dossier
            is used.

    Returns:
        None
    """

    first_semantic_document: SemanticDocument = semantic_documents_queryset.first()

    # Don't merge there is only one doc, technically not needed, as we filter
    # out in calculate_bekb_dossier_merge_groupings
    if semantic_documents_queryset.count() > 1:
        # Merge pages into first document
        rest_of_semantic_documents = semantic_documents_queryset.exclude(
            uuid=first_semantic_document.uuid
        )
        if rest_of_semantic_documents.exists():
            if user is None:
                user = first_semantic_document.dossier.owner

            rest_docs = list(rest_of_semantic_documents.all())
            for adoc in rest_docs:
                sempages = SemanticPage.objects.filter(semantic_document=adoc).order_by(
                    "number"
                )
                copy_move_semantic_pages_from_queryset(
                    semantic_document=first_semantic_document,
                    semantic_pages=sempages,
                    move=True,
                    user=user,
                )
            # Delete other docs if they still exist
            rest_of_semantic_documents.update(deleted_at=timezone.now())

    # update category for remaining semantic doc
    if first_semantic_document.document_category.name != document_category_name:
        document_category = DocumentCategory.objects.get(
            name=document_category_name,
            account=first_semantic_document.document_category.account,
        )
        first_semantic_document.document_category = document_category

    # update suffix for remaining semantic doc

    suffix_map = {
        "DE": "(zusammengefasst)",
        "EN": "(combined)",
        "FR": " (combiné)",
        "IT": " (combinato)",
    }

    first_semantic_document.title_suffix = suffix_map.get(
        first_semantic_document.dossier.lang, "(zusammengefasst)"
    )

    first_semantic_document.save()

    # We include soft deleted pages to have consistently unique page numbers in the whole account
    fix_document_page_numbering(
        document=first_semantic_document,
        fix_non_sequential_pages=True,
        include_soft_deleted_pages=True,
    )


def partition_grouping_collateral_assignment(
    semantic_documents_queryset: QuerySet[SemanticDocument],
) -> List[QuerySet[SemanticDocument]]:
    """
    Handle grouping of semantic documents by their collateral assignment. When we merge semantic documents,
    we don't want to lose the collateral assignment. We therefore partition the semantic documents so
    that they contain at most one collateral in their assignments

    :param semantic_documents_queryset: List of semantic documents which is ordered already in the order
    of the expected result. This list will be partitioned if necessary.
    If not necessary, a list with a single partition will be returned

    @return List of queryset for semantic documents.
    - Each list has exactly one or zero distinct collaterals assigned in its documents.
    - Only the first list can contain semantic documents without collateral assignments.
    -
    """
    partitions_map: dict[str, QuerySet[SemanticDocument]] = {}

    # List of all distince collaterals that are assigned to these semantic documents
    unique_collateral_assignments = (
        semantic_documents_queryset.values_list(
            "collateralassignment__collateral", flat=True
        )
        .exclude(collateralassignment__collateral=None)
        .distinct()
    )

    if unique_collateral_assignments.count() < 1:
        # They all have the same collateral assignment or no assignment
        return [semantic_documents_queryset]

    # Partition the semantic documents that have collateral assignments into lists with each list having
    # exactly one collateral assigned
    for collateral_assignment_uuid in unique_collateral_assignments:
        partitions_map[str(collateral_assignment_uuid)] = (
            semantic_documents_queryset.filter(
                collateralassignment__collateral__uuid=collateral_assignment_uuid
            )
        )

    # Append all docs with no_collaterals to the end of the first query set
    semantic_documents_no_collaterals = semantic_documents_queryset.filter(
        collateralassignment__collateral__isnull=True
    )
    if semantic_documents_no_collaterals.exists():
        partitions_map[
            str(unique_collateral_assignments[0])
        ] = semantic_documents_queryset.filter(
            Q(collateralassignment__collateral__uuid=unique_collateral_assignments[0])
            | Q(collateralassignment__collateral__isnull=True)
        ).order_by(
            F("collateralassignment__collateral__collateral_number").desc(
                nulls_last=True
            )
        )

    return list(partitions_map.values())


@transaction.atomic
def merge_bekb_semdoc_grouping_for_archive(
    semantic_document_map: dict[str, QuerySet[SemanticDocument]],
    user: Optional[AbstractUser] = None,
    handle_collateral_assignment: bool = True,
    extra_logging=True,
):
    """
    Move pages of several documents together defined by the structure in semantic_document_map
    @param handle_collateral_assignment:
    @param semantic_document_map:
    @param user:
    @return:
    """
    # Handle empty case, otherwise getting a dossier will fail
    if semantic_document_map == {}:
        return

    dossier = next(iter(semantic_document_map.values())).first().dossier
    sem_pages_count = dossier.semantic_pages.count()
    sem_pages_uuids = dossier.semantic_pages.values_list("uuid", flat=True)

    if extra_logging:
        logger.info(
            "Initial semantic documents",
            dossier_uuid=dossier.uuid,
            semantic_documents=dossier.semantic_documents.all(),
            semantic_pages=SemanticPage.objects.filter(dossier=dossier)
            .order_by("semantic_document", "number")
            .values(
                "semantic_document",
                "number",
                "document_category__name",
                "processed_page__number",
            ),
        )

    for (
        document_category_name,
        semantic_documents_queryset,
    ) in semantic_document_map.items():

        if handle_collateral_assignment:
            for partitioned_queryset in partition_grouping_collateral_assignment(
                semantic_documents_queryset=semantic_documents_queryset,
            ):
                merge_semantic_document_grouping_queryset_for_archive(
                    document_category_name=document_category_name,
                    semantic_documents_queryset=partitioned_queryset,
                    user=user,
                )
        else:
            merge_semantic_document_grouping_queryset_for_archive(
                document_category_name=document_category_name,
                semantic_documents_queryset=semantic_documents_queryset,
                user=user,
            )

    new_sem_pages_count = dossier.semantic_pages.filter(
        uuid__in=sem_pages_uuids
    ).count()

    if new_sem_pages_count != sem_pages_count:
        msg = f"Number of semantic pages changed after grouping, expected {sem_pages_count}, got {new_sem_pages_count}"
        logger.error(msg)
        raise Exception(msg)

    if extra_logging:
        logger.info(
            "Merged semantic documents",
            dossier_uuid=dossier.uuid,
            semantic_documents=dossier.semantic_documents.all(),
            semantic_pages=SemanticPage.objects.filter(dossier=dossier)
            .order_by("semantic_document", "number")
            .values(
                "semantic_document",
                "number",
                "document_category__name",
                "processed_page__number",
            ),
        )


def check_work_status_transition_consistency_bekb_archiving(dossier_uuid):
    """
    Do the default check for consistency and then do another loop with BEKB specific stuff.
    """
    list_of_transitions, events, e = check_work_status_transition_consistency(
        dossier_uuid
    )

    list_of_transitions = []
    inconsistent_event = None
    for e in events:
        from_key = e.details["from_state_key"]
        to_key = e.details["to_state_key"]
        username = e.details["username"]
        list_of_transitions.append((from_key, to_key, username))

        if to_key == "EXPORT_ARCHIVE_AVAILABLE":
            if from_key not in VALID_READY_FOR_EXPORT_STATUS_KEYS_MORTGAGE:
                logger.error(
                    "Invalid transition to export archive",
                    e=e,
                    dossier_uuid=dossier_uuid,
                    list_of_transitions=list_of_transitions,
                )

    return list_of_transitions, events, inconsistent_event
