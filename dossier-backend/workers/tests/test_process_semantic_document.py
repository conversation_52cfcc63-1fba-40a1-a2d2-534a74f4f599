import logging
import structlog
from pathlib import Path
from tempfile import TemporaryDirectory
from urllib.parse import urlparse
from uuid import uuid4
from zipfile import ZipFile

import pytest
import requests_mock
from asgiref.sync import sync_to_async

from assets import ASSETS_PATH
from dossier.helpers import create_dossier_file_without_saving
from dossier.helpers_v2 import prepare_semantic_dossier
from dossier.models import Dossier
from dossier.schemas import Seman<PERSON><PERSON><PERSON><PERSON>, SemanticDocumentFullApiData
from workers.schemas import SemanticDocumentPDFRequestV1, SemanticDocumentPDFResponseV1
from workers.services.semantic_document_pdf_export import (
    worker_generate_semantic_document_pdf,
    SemanticDocumentExport,
)
from workers.workers import (
    async_process_semantic_dossier_pdf_request,
)

logging.basicConfig(level=logging.INFO)

logger = structlog.get_logger()


def prepare_data(dossier_name: str):
    logger.info(f"Now prepare data for dossier '{dossier_name}'")
    dossier = Dossier.objects.get(name=dossier_name)
    semantic_dossier = prepare_semantic_dossier(
        dossier=dossier, include_annotations=True
    )

    export_request_uuid = uuid4()
    dossier_file = create_dossier_file_without_saving(dossier, "sample.pdf")

    data = semantic_dossier.model_dump()
    data["uuid"] = str(data["uuid"])
    return export_request_uuid, SemanticDossier(**data), dossier_file


async def async_prepare_data(dossier_name: str):
    return await sync_to_async(prepare_data)(dossier_name)


def content_callback(request, context):
    parts = urlparse(request.path_url)
    assets_dossier_file = (
        ASSETS_PATH / f'sample_dossiers/{parts.path.split("/")[2]}.zip'
    )
    assert assets_dossier_file.exists()

    file_path = parts.path.split("/")[2:]
    path_in_zip = Path(*file_path)

    with ZipFile(assets_dossier_file, "r") as zip:
        return zip.read(name=str(path_in_zip))


@pytest.mark.asyncio
# @pytest.mark.skip(reason="Broken")
async def test_semantic_dossier_pdf_request_response_without_rabbitmq(db):
    export_request_uuid, request_dossier, dossier_file = await async_prepare_data(
        "sales pitch mix with errors dossier"
    )

    # Select ONE semantic document, that we want to process
    semantic_document: SemanticDocumentFullApiData = request_dossier.semantic_documents[
        0
    ]

    with requests_mock.Mocker() as m:
        m.get(requests_mock.ANY, content=content_callback)
        m.put(requests_mock.ANY)

        request = SemanticDocumentPDFRequestV1(
            semantic_document_pdf_request_uuid=export_request_uuid,
            semantic_dossier=request_dossier,
            semantic_document_uuid=semantic_document.uuid,
            put_upload_url=dossier_file.put_url,
        )

        raw_response = await async_process_semantic_dossier_pdf_request(
            semantic_document_pdf_request=request.model_dump_json()
        )

        # assert request_calls.call_count == 1
        # assert put_call.call_count == 1

    response = SemanticDocumentPDFResponseV1.model_validate_json(raw_response)

    assert response.semantic_document_pdf_request_uuid == export_request_uuid


@pytest.mark.skip(reason="Test works locally but not on CI")
def test_generate_semantic_document_pdf(db):
    # FAILS with:
    # workers/semantic_document_pdf_export.py:89: in generate_semantic_document_pdf
    #     pdf_reader: PdfReader = pypdf.PdfReader(
    # /usr/local/lib/python3.10/site-packages/pypdf/_reader.py:317: in __init__
    #     self.read(stream)
    # /usr/local/lib/python3.10/site-packages/pypdf/_reader.py:1409: in read
    #     self._find_eof_marker(stream)
    # _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
    # self = <pypdf._reader.PdfReader object at 0x7fc2b7c4ac80>
    # stream = <_io.BytesIO object at 0x7fc2b74c1940>
    #     def _find_eof_marker(self, stream: StreamType) -> None:
    #         last_mb = 8  # to parse whole file
    #         line = b""
    #         while line[:5] != b"%%EOF":
    #             if stream.tell() < last_mb:
    # >               raise PdfReadError("EOF marker not found")
    # E               pypdf.errors.PdfReadError: EOF marker not found
    #
    # Looks like it's an issue with what's returned by BytesIO(requests.get(pdf_page_url).content
    # We could mock this, but it sort of defeats the purpose of the test
    export_request_uuid, request_dossier, dossier_file = prepare_data(
        "sales pitch mix with errors dossier"
    )

    # Select ONE semantic document, that we want to process
    semantic_document: SemanticDocumentFullApiData = request_dossier.semantic_documents[
        0
    ]

    request = SemanticDocumentPDFRequestV1(
        semantic_document_pdf_request_uuid=export_request_uuid,
        semantic_dossier=request_dossier,
        semantic_document_uuid=semantic_document.uuid,
        put_upload_url=dossier_file.put_url,
    )

    with TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        semantic_document_export: SemanticDocumentExport = (
            worker_generate_semantic_document_pdf(
                semantic_dossier=request.semantic_dossier,
                semantic_document_uuid=request.semantic_document_uuid,
                dest_path=temp_path,
                add_metadata_json=True,
                add_uuid_suffix=False,
            )
        )
        assert "Document_sales_pitch_mix_with_errors_dossier.pdf" in str(
            semantic_document_export.path
        )

        semantic_document_export: SemanticDocumentExport = (
            worker_generate_semantic_document_pdf(
                semantic_dossier=request.semantic_dossier,
                semantic_document_uuid=request.semantic_document_uuid,
                dest_path=temp_path,
                add_metadata_json=True,
                add_uuid_suffix=True,
            )
        )
        assert (
            f"Document_sales_pitch_mix_with_errors_dossier_{request.semantic_document_uuid}.pdf"
            in str(semantic_document_export.path)
        )
