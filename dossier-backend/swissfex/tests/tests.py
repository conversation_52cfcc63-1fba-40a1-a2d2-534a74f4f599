import json
import uuid
from pathlib import Path
import random
from datetime import timed<PERSON><PERSON>
import pytest
import structlog
from django.conf import settings
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from django.utils import timezone
import jwt
from pytest_mock import MockerFixture
import dossier.schemas as dossier_schemas
from dossier.fakes import (
    add_some_fake_semantic_documents,
)
from dossier.models import Dossier, OriginalFile, DocumentCategory, DossierCopyStatus
from dossier.management.commands.dossier_event_consumer_v2 import (
    copy_dossier as copy_dossier_consumer,
    set_semantic_document_export_done,
    set_dossier_zipper_export_done,
    copy_dossier_into_existing_dossier,
)
from dossier.tests.test_utils import mock_publish_side_effect_services_rabbit_mq_publish
from dossier_zipper.schemas import DossierZipRequestV1
from dossier_zipper.workers import process_dossier_zip_request
from projectconfig.settings import DEFAULT_VALID_UI_LANGUAGES
from semantic_document.models import SemanticDocument, SemanticPage
from swissfex.schemas import schemas
from swissfex.conftest import AuthenticatedClient
from swissfex.tests.helpers_test import (
    swissfex_api_post_create_dossier,
    swissfex_api_post_add_original_file,
    swissfex_api_get_semantic_documents,
)
from workers.models import SemanticDocumentExport
from semantic_document import (
    schemas as semantic_document_schemas,
)
from workers.schemas import SemanticDocumentPDFRequestV1
from workers.workers import process_semantic_dossier_pdf_request
from dossier_zipper.conftest import mocked_get_dossier  # noqa: F401
from semantic_document.models import SemanticPageUserAnnotations
import pypdf
import requests
from io import BytesIO


logger = structlog.get_logger(__name__)

pytestmark = pytest.mark.django_db


def test_create_dossier_api_success(
    authenticated_client, swissfex_account, set_swissfex_JWK
):
    # Base case to check whether we can create a dossier
    result, external_dossier_id = swissfex_api_post_create_dossier(authenticated_client)

    assert result.status_code == 201

    dossier = Dossier.objects.get(
        external_id=external_dossier_id, account__key=swissfex_account.key
    )
    parsed = schemas.Dossier.model_validate_json(result.content)
    assert parsed.external_dossier_id == external_dossier_id
    assert parsed.uuid == dossier.uuid

    # Test for conflict when dossier already exists
    result, external_dossier_id2 = swissfex_api_post_create_dossier(
        authenticated_client
    )
    assert result.status_code == 409


def test_update_dossier_api_success(
    authenticated_client, swissfex_account, set_swissfex_JWK
):
    # Base case to check whether we can create a dossier
    result, external_dossier_id = swissfex_api_post_create_dossier(authenticated_client)

    assert result.status_code == 201

    # Test changing two params
    result = authenticated_client.patch(
        path=reverse("swissfex-api:update-dossier"),
        data=schemas.ChangeDossier(
            name="Test Dossier 2",
            external_dossier_id=external_dossier_id,
            lang="fr",
        ).model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 201

    dossier = Dossier.objects.get(external_id=external_dossier_id)
    assert dossier.name == "Test Dossier 2"
    assert dossier.lang == "fr"

    # Test changing one param
    result = authenticated_client.patch(
        path=reverse("swissfex-api:update-dossier"),
        data=schemas.ChangeDossier(
            name="Test Dossier 3",
            external_dossier_id=external_dossier_id,
        ).model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 201

    dossier = Dossier.objects.get(external_id=external_dossier_id)
    assert dossier.name == "Test Dossier 3"
    # lang should not be changed
    assert dossier.lang == "fr"


@pytest.mark.parametrize(
    ("token_overwrite", "expected"),
    [
        ({}, 201),  # Base case
        ({"user_roles": ""}, 401),  # No user roles
        ({"account_key": "wrong"}, 401),  # Wrong account key
        ({"exp": 1}, 401),  # Expired token
        ({"aud": ""}, 401),  # Aud not set
    ],
)
def test_create_dossier_api_authentication(
    token_overwrite,
    expected,
    swissfex_account,
    mock_jwks_pem_public_private,
    token_data,
    set_swissfex_JWK,
):
    # Test various cases for JWT authentication
    url = reverse("swissfex-api:create-dossier")

    dossier_data = schemas.CreateDossier(
        name="Test Dossier", external_dossier_id=str(uuid.uuid4()), language="de"
    ).model_dump()

    # Use the token_overwrite to overwrite certain fields in the token to ensure JWT authentication is working
    authenticated_client = AuthenticatedClient(
        jwt.encode(
            payload={
                "aud": "account",
                "user_roles": [settings.API_ROLE],
                **token_data,
                **token_overwrite,
            },
            key=mock_jwks_pem_public_private,
            algorithm="RS256",
        )
    )

    result = authenticated_client.post(
        path=url,
        data={**dossier_data},
        content_type="application/json",
    )

    assert result.status_code == expected


def test_create_to_delete_dossier_api_success(
    authenticated_client, swissfex_account, set_swissfex_JWK
):
    # Test creating and deleting a dossier via API

    result, external_dossier_id = swissfex_api_post_create_dossier(authenticated_client)
    assert result.status_code == 201

    assert Dossier.objects.get(
        external_id=external_dossier_id,
        account__key=swissfex_account.key,
    )

    result = authenticated_client.delete(
        path=reverse(
            "swissfex-api:dossier-delete",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
    )

    assert result.status_code == 200

    # For some reason, setting expiry date to past causes object deletion.
    # Likely some sort of django signal
    assert (
        Dossier.objects.filter(
            external_id=external_dossier_id,
            account__key=swissfex_account.key,
        ).count()
        == 0
    )


@pytest.mark.django_db
def test_delete_dossier_api_failure(
    authenticated_client, swissfex_account, set_swissfex_JWK
):
    # Expect failure when trying to delete a dossier that does not exist
    result = authenticated_client.delete(
        path=reverse(
            "swissfex-api:dossier-delete",
            kwargs={"external_dossier_id": str(uuid.uuid4())},
        )
    )

    assert result.status_code == 404


def test_add_original_file(
    authenticated_client, swissfex_account, mocker: MockerFixture, set_swissfex_JWK
):
    # Create a dossier
    response, external_dossier_id = swissfex_api_post_create_dossier(
        authenticated_client
    )
    assert response.status_code == 201

    file = SimpleUploadedFile(
        "test_page1.jpg", b"file_content1", content_type="image/jpeg"
    )

    process_original_file_mock = mocker.patch(
        "dossier.services_external.process_original_file"
    )
    response = swissfex_api_post_add_original_file(
        authenticated_client, external_dossier_id, data={"file": file}
    )

    assert response.status_code == 201
    original_file = dossier_schemas.CreatedObjectReference(**response.json())

    original_file = OriginalFile.objects.get(uuid=original_file.uuid)

    assert process_original_file_mock.call_args_list[0].args[0] == original_file

    # same file should not be allowed by default
    response = swissfex_api_post_add_original_file(
        authenticated_client, external_dossier_id, data={"file": file}
    )
    assert response.status_code == 409
    assert (
        "The file test_page1.jpg already exists in the dossier"
        in response.json()["detail"]
    )

    # Use parameter to allow duplicate files
    response = swissfex_api_post_add_original_file(
        authenticated_client,
        external_dossier_id,
        data={"file": file, "allow_duplicate_and_rename": True},
    )
    assert response.status_code == 201

    duplicate_and_rename_original_file_response = (
        dossier_schemas.CreatedObjectReference(**response.json())
    )

    duplicate_and_rename_original_file = OriginalFile.objects.get(
        uuid=duplicate_and_rename_original_file_response.uuid
    )

    # Check that file model objects are different
    assert original_file.uuid != duplicate_and_rename_original_file.uuid

    # Check that new duplicate file has new name
    assert original_file.file.name != duplicate_and_rename_original_file.file.name

    # Check that they belong to the same dossier
    assert original_file.dossier == duplicate_and_rename_original_file.dossier


def test_add_original_file_with_force_params(
    authenticated_client, swissfex_account, mocker: MockerFixture, set_swissfex_JWK
):
    # Create a dossier
    response, external_dossier_id = swissfex_api_post_create_dossier(
        authenticated_client
    )
    assert response.status_code == 201

    file = SimpleUploadedFile(
        "test_page1.jpg", b"file_content1", content_type="image/jpeg"
    )

    mocker.patch("dossier.services_external.process_original_file")

    ext_doc_id = "hohoho42"
    response = swissfex_api_post_add_original_file(
        authenticated_client,
        external_dossier_id,
        data={
            "file": file,
            "force_external_semantic_document_id": ext_doc_id,
            "allow_duplicate_and_rename": True,
            "force_access_mode": "read_only",
            "force_semantic_document_custom_attribute": "this is a custom attribute",
        },
    )
    assert response.status_code == 201

    of = OriginalFile.objects.get(force_external_semantic_document_id=ext_doc_id)
    assert of
    assert of.force_access_mode == OriginalFile.OriginalFileForceAccessMode.READ_ONLY
    assert of.force_semantic_document_custom_attribute == "this is a custom attribute"


def test_get_file_status(
    authenticated_client,
    swissfex_account,
    document_categories,
    mocker: MockerFixture,
    set_swissfex_JWK,
):
    # Create a dossier
    result, external_dossier_id = swissfex_api_post_create_dossier(authenticated_client)

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    file_path = Path(__file__).parent / "data/240_betreibungsauskunft-mt-at.pdf"
    file = SimpleUploadedFile(
        file_path.name, file_path.read_bytes(), content_type="application/octet-stream"
    )

    # Upload a file
    mocker.patch("dossier.services_external.process_original_file")
    response = swissfex_api_post_add_original_file(
        authenticated_client, external_dossier_id, data={"file": file}
    )

    OriginalFile.objects.get(uuid=response.json()["uuid"])

    random.seed(42)
    add_some_fake_semantic_documents(
        dossier=dossier, allow_empty_docs=False, num_docs=5
    )

    response = authenticated_client.get(
        path=reverse(
            "swissfex-api:file-status",
            kwargs={"external_dossier_id": external_dossier_id},
        )
    )

    parsed = schemas.DossierProcessingStatus.model_validate_json(response.content)
    assert parsed.dossier_uuid == dossier.uuid
    assert len(parsed.original_files) == 2

    for original_file in parsed.original_files:
        original_file_object = OriginalFile.objects.get(uuid=original_file.uuid)
        extracted_files = original_file_object.extractedfile_set.all()

        assert len(original_file.extracted_files) == len(extracted_files)

        for extracted_file in extracted_files:
            assert extracted_file.uuid in [
                extracted_file.uuid for extracted_file in original_file.extracted_files
            ]

    # Check that 50% of original files are processed
    assert parsed.progress == 50


def test_get_dossier_details_api(
    authenticated_client, swissfex_account, set_swissfex_JWK
):
    # Create a dossier
    result, external_dossier_id = swissfex_api_post_create_dossier(authenticated_client)
    assert result.status_code == 201

    response = authenticated_client.get(
        path=reverse(
            "swissfex-api:dossier-details",
            kwargs={"external_dossier_id": external_dossier_id},
        )
    )

    parsed = schemas.Dossier.model_validate_json(response.content)
    assert parsed.external_dossier_id == external_dossier_id

    assert (
        authenticated_client.get(
            path=reverse(
                "swissfex-api:dossier-details",
                kwargs={"external_dossier_id": str(uuid.uuid4())},
            )
        ).status_code
        == 404
    )

    # Set the dossier to be expired
    dossier = Dossier.objects.get(external_id=external_dossier_id)
    dossier.expiry_date = timezone.now() - timedelta(days=1)
    dossier.save()
    response = authenticated_client.get(
        path=reverse(
            "swissfex-api:dossier-details",
            kwargs={"external_dossier_id": external_dossier_id},
        )
    )

    assert response.status_code == 404


def test_get_semantic_documents_api_success(
    authenticated_client, swissfex_account, document_categories, set_swissfex_JWK
):
    # Create a dossier
    result, external_dossier_id = swissfex_api_post_create_dossier(authenticated_client)
    assert result.status_code == 201

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    semantic_documents = add_some_fake_semantic_documents(
        dossier=dossier, allow_empty_docs=False
    )

    response, parsed = swissfex_api_get_semantic_documents(
        authenticated_client, external_dossier_id
    )
    assert response.status_code == 200
    assert len(semantic_documents) == len(parsed)

    documents_set = {doc.uuid for doc in parsed}
    for semantic_document in semantic_documents:
        assert semantic_document.uuid in documents_set

    response, parsed = swissfex_api_get_semantic_documents(
        authenticated_client, external_dossier_id, show_pages=True
    )
    assert response.status_code == 200

    # Check content of response
    for res_sem_doc in parsed:
        assert res_sem_doc.title
        assert res_sem_doc.title_lang in DEFAULT_VALID_UI_LANGUAGES
        assert int(res_sem_doc.document_category_id) > 0
        assert res_sem_doc.document_category_key
        assert res_sem_doc.document_category_title_de
        assert res_sem_doc.document_category_title_en
        assert res_sem_doc.document_category_title_fr
        assert res_sem_doc.document_category_title_it
        assert res_sem_doc.access_mode == dossier_schemas.AccessMode.READ_WRITE

        diff_1 = (res_sem_doc.updated_at - res_sem_doc.created_at).total_seconds()
        assert 0 <= diff_1 < 0.1  # can also be 0
        diff_2 = (res_sem_doc.last_change - res_sem_doc.created_at).total_seconds()
        assert (
            0 <= diff_2 < 0.1
        )  # larger 0 because some pages have been added afterwards

    # Check that all documents and pages are contained in the response
    documents_set = {doc.uuid for doc in parsed}
    pages_set = {page.uuid for doc in parsed for page in doc.semantic_pages}

    for semantic_document in semantic_documents:
        assert semantic_document.uuid in documents_set
        for page in semantic_document.semantic_pages.all():
            assert page.uuid in pages_set


def test_get_semantic_documents_api_failure(
    authenticated_client, swissfex_account, document_categories, set_swissfex_JWK
):
    # Test the case where the dossier does not exist
    external_dossier_id1 = str(uuid.uuid4())
    response, parsed = swissfex_api_get_semantic_documents(
        authenticated_client, external_dossier_id1, 404
    )
    assert response.status_code == 404

    # Create a dossier, but don't add any semantic documents
    result, external_dossier_id = swissfex_api_post_create_dossier(authenticated_client)
    assert result.status_code == 201

    response, parsed = swissfex_api_get_semantic_documents(
        authenticated_client, external_dossier_id, show_pages=True
    )
    assert response.status_code == 200
    assert response.json() == []


def test_get_document_categories_api_success(
    authenticated_client, swissfex_account, document_categories, set_swissfex_JWK
):
    response = authenticated_client.get(
        path=reverse("swissfex-api:document-categories"),
    )

    assert response.status_code == 200

    # Check we can parse the response
    schemas.DocumentCategories.model_validate_json(response.content)

    for document_category in document_categories:
        if document_category.account.key == "swissfex":
            assert document_category.name in response.json().keys()


def test_get_document_categories_api_empty(
    authenticated_client, swissfex_account, set_swissfex_JWK
):
    # Test the case where there are no document categories
    DocumentCategory.objects.all().delete()
    response = authenticated_client.get(
        path=reverse("swissfex-api:document-categories"),
    )

    assert response.status_code == 200

    assert response.json() == {}


@pytest.mark.parametrize(
    ("completed"),
    [False, True],
)
def test_export_dossier_export_success(
    completed,
    mocked_get_dossier,
    authenticated_client,
    swissfex_account,
    document_categories,
    set_swissfex_JWK,
    mocker: MockerFixture,
):
    # Create a dossier
    result, external_dossier_id = swissfex_api_post_create_dossier(authenticated_client)
    assert result.status_code == 201

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    add_some_fake_semantic_documents(dossier=dossier)

    def mock_publish_side_effect(*args, **kwargs):
        request = DossierZipRequestV1.model_validate_json(kwargs["message"])
        response_json = process_dossier_zip_request(dossier_zip_request=request)

        if completed:
            set_dossier_zipper_export_done(response_json)

    mock_dispatch_publish_request = mocker.patch(
        "swissfex.api.publish",
        side_effect=mock_publish_side_effect,
    )

    # Request to generate an export
    response = authenticated_client.post(
        path=reverse(
            "swissfex-api:export-dossier-export",
        ),
        data=json.dumps({"external_dossier_id": external_dossier_id}),
        content_type="application/json",
    )

    assert response.status_code == 200
    mock_dispatch_publish_request.assert_called_once()

    first_export_uuid = dossier.exports.first().uuid

    assert response.status_code == 200
    parse = schemas.ExportRequest.model_validate_json(response.content)
    assert parse.export_uuid == first_export_uuid

    # Test that we can handle a duplicate request - should return a new export UUID
    response = authenticated_client.post(
        path=reverse(
            "swissfex-api:export-dossier-export",
        ),
        data=json.dumps({"external_dossier_id": external_dossier_id}),
        content_type="application/json",
    )
    assert response.status_code == 200
    # We should get the different export UUID
    parse = schemas.ExportRequest.model_validate_json(response.content)
    assert parse.export_uuid != first_export_uuid
    second_export_uuid = dossier.exports.order_by("-updated_at").first().uuid
    assert parse.export_uuid == second_export_uuid

    # Check we can poll for dossier status
    response = authenticated_client.get(
        path=reverse(
            "swissfex-api:dossier-export-status",
            kwargs={"export_uuid": str(second_export_uuid)},
        ),
    )

    assert response.status_code == 200

    parse = schemas.ExportStatus.model_validate_json(response.content)
    assert parse.export_uuid == second_export_uuid
    assert parse.dossier_url

    # Add some more semantic documents
    add_some_fake_semantic_documents(dossier=dossier)

    response = authenticated_client.post(
        path=reverse(
            "swissfex-api:export-dossier-export",
        ),
        data=json.dumps({"external_dossier_id": external_dossier_id}),
        content_type="application/json",
    )

    assert response.status_code == 200
    parse = schemas.ExportRequest.model_validate_json(response.content)
    assert parse.export_uuid != first_export_uuid
    assert parse.export_uuid != second_export_uuid
    # Check that we fetched the latest dossier export
    assert parse.export_uuid == dossier.exports.order_by("-updated_at").first().uuid


@pytest.mark.django_db
def test_export_dossier_export_success_with_no_files(
    mocked_get_dossier,
    authenticated_client,
    swissfex_account,
    document_categories,
    set_swissfex_JWK,
):
    # Create a dossier
    result, external_dossier_id = swissfex_api_post_create_dossier(authenticated_client)
    assert result.status_code == 201

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    # Request to generate an export with no documents
    response = authenticated_client.post(
        path=reverse(
            "swissfex-api:export-dossier-export",
        ),
        data=json.dumps({"external_dossier_id": external_dossier_id}),
        content_type="application/json",
    )

    assert response.status_code == 400
    assert (
        response.json()["detail"]
        == f"Dossier {dossier.uuid} with external id {dossier.external_id} does not have any files"
    )


def test_dossier_end_to_end(
    mocked_get_dossier,
    authenticated_client,
    swissfex_account,
    document_categories,
    mocker: MockerFixture,
    set_swissfex_JWK,
):
    """Test the whole process of creating a dossier, adding documents, exporting and checking status"""

    # Create a dossier
    result, external_dossier_id = swissfex_api_post_create_dossier(authenticated_client)
    assert result.status_code == 201

    file_path = Path(__file__).parent / "data/240_betreibungsauskunft-mt-at.pdf"

    file = SimpleUploadedFile(
        file_path.name, file_path.read_bytes(), content_type="application/octet-stream"
    )

    process_original_file_mock = mocker.patch(
        "dossier.services_external.process_original_file"
    )
    response = swissfex_api_post_add_original_file(
        authenticated_client,
        external_dossier_id,
        data={"file": file, "force_title_suffix": "test"},
    )
    assert response.status_code == 201

    original_file = dossier_schemas.CreatedObjectReference(**response.json())
    original_file = OriginalFile.objects.get(uuid=original_file.uuid)
    assert process_original_file_mock.call_args_list[0].args[0] == original_file

    add_some_fake_semantic_documents(
        Dossier.objects.get(external_id=external_dossier_id),
        num_docs=1,
        max_pages=2,
        min_num_pages=2,
        no_page_objects_per_page=1,
        valid_document_category_keys=["PLAN_FLOOR"],
        allow_empty_docs=False,
    )

    def mock_publish_side_effect(*args, **kwargs):
        request = DossierZipRequestV1.model_validate_json(kwargs["message"])
        response_json = process_dossier_zip_request(dossier_zip_request=request)

        set_dossier_zipper_export_done(response_json)

    mocker.patch(
        "swissfex.api.publish",
        side_effect=mock_publish_side_effect,
    )

    # Request to generate an export
    response = authenticated_client.post(
        path=reverse(
            "swissfex-api:export-dossier-export",
        ),
        data=json.dumps({"external_dossier_id": external_dossier_id}),
        content_type="application/json",
    )

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    assert response.status_code == 200

    parse = schemas.ExportRequest.model_validate_json(response.content)

    assert parse.export_uuid == dossier.exports.first().uuid

    # Check we can poll for dossier status
    response = authenticated_client.get(
        reverse(
            "swissfex-api:dossier-export-status",
            kwargs={"export_uuid": response.json()["export_uuid"]},
        ),
    )

    parse = schemas.ExportStatus.model_validate_json(response.content)

    assert parse.export_uuid == dossier.exports.first().uuid
    assert parse.dossier_url == dossier.exports.first().file.fast_url


def test_soft_delete_semantic_document(
    authenticated_client,
    swissfex_account,
    document_categories,
    set_swissfex_JWK,
):
    # Test deleting and recovering a semantic document
    external_dossier_id = str(uuid.uuid4())

    dossier = Dossier.objects.create(
        external_id=external_dossier_id, account=swissfex_account
    )

    document_category = DocumentCategory.objects.filter(account=dossier.account).first()

    semantic_documents = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=[document_category.name],
        max_pages=5,
        min_num_pages=5,
    )

    # Get Semantic documents
    response, parsed = swissfex_api_get_semantic_documents(
        authenticated_client, external_dossier_id, show_pages=True
    )
    assert response.status_code == 200

    assert len(parsed) == 1
    assert len(parsed[0].semantic_pages) == 5
    assert parsed[0].uuid == semantic_documents[0].uuid

    response = authenticated_client.delete(
        path=f"{reverse('swissfex-api:semantic-document-soft-delete',kwargs={'external_dossier_id': external_dossier_id, 'semantic_document_uuid': semantic_documents[0].uuid})}",
    )

    parsed = semantic_document_schemas.SavingResultWithMessage.model_validate_json(
        response.content
    )

    assert parsed.message == "deleted"
    assert response.status_code == 200

    # Note trick is here to get the object from the deleted objects manager (all objects)
    # otherwise we will not see soft deleted objects
    soft_deleted_semantic_document = SemanticDocument.all_objects.get(
        uuid=semantic_documents[0].uuid
    )
    assert soft_deleted_semantic_document.deleted_at is not None

    # Check we can't fetch it
    response, parsed = swissfex_api_get_semantic_documents(
        authenticated_client, external_dossier_id, show_pages=True
    )
    assert response.status_code == 200

    assert len(parsed) == 0

    # Check we can recover it

    response = authenticated_client.put(
        path=f"{reverse('swissfex-api:semantic-document-restore',kwargs={'external_dossier_id': external_dossier_id, 'semantic_document_uuid': semantic_documents[0].uuid})}",
    )

    parsed = semantic_document_schemas.SavingResultWithMessage.model_validate_json(
        response.content
    )

    assert parsed.message == "restored"
    assert response.status_code == 200

    restored_semantic_document = SemanticDocument.objects.get(
        uuid=semantic_documents[0].uuid
    )
    assert restored_semantic_document.deleted_at is None

    # Check we can fetch it
    response, parsed = swissfex_api_get_semantic_documents(
        authenticated_client, external_dossier_id, show_pages=True
    )
    assert response.status_code == 200

    assert len(parsed) == 1
    assert len(parsed[0].semantic_pages) == 5
    assert parsed[0].uuid == semantic_documents[0].uuid


def test_soft_delete_semantic_page(
    authenticated_client,
    swissfex_account,
    document_categories,
    set_swissfex_JWK,
):
    # Test deleting and recovering a semantic page
    external_dossier_id = str(uuid.uuid4())

    dossier = Dossier.objects.create(
        external_id=external_dossier_id, account=swissfex_account
    )

    document_category = DocumentCategory.objects.filter(account=dossier.account).first()

    semantic_documents = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=[document_category.name],
        max_pages=5,
        min_num_pages=5,
    )

    # Get Semantic documents and associated pages
    response, parsed = swissfex_api_get_semantic_documents(
        authenticated_client, external_dossier_id, show_pages=True
    )
    assert response.status_code == 200

    assert len(parsed) == 1
    assert len(parsed[0].semantic_pages) == 5
    assert parsed[0].uuid == semantic_documents[0].uuid

    semantic_page = parsed[0].semantic_pages[0]

    response = authenticated_client.delete(
        path=f"{reverse('swissfex-api:semantic-page-soft-delete',kwargs={'external_dossier_id': external_dossier_id, 'semantic_page_uuid': str(semantic_page.uuid)})}",
    )

    parsed = semantic_document_schemas.SavingResultWithMessage.model_validate_json(
        response.content
    )

    assert parsed.message == "deleted"
    assert response.status_code == 200

    # Note trick is here to get the object from the deleted objects manager (all objects)
    # otherwise we will not see soft deleted objects
    soft_deleted_semantic_page = SemanticPage.all_objects.get(uuid=semantic_page.uuid)
    assert soft_deleted_semantic_page.deleted_at is not None

    # Check page is missing in fetch
    response, parsed = swissfex_api_get_semantic_documents(
        authenticated_client, external_dossier_id, show_pages=True
    )
    assert response.status_code == 200

    assert len(parsed[0].semantic_pages) == 4

    assert str(semantic_page.uuid) not in [
        str(page.uuid) for page in parsed[0].semantic_pages
    ]

    # Check we can recover it
    response = authenticated_client.put(
        path=f"{reverse('swissfex-api:semantic-page-restore',kwargs={'external_dossier_id': external_dossier_id, 'semantic_page_uuid': semantic_page.uuid})}",
    )

    parsed = semantic_document_schemas.SavingResultWithMessage.model_validate_json(
        response.content
    )

    assert parsed.message == "restored"
    assert response.status_code == 200

    restored_semantic_document = SemanticDocument.objects.get(
        uuid=semantic_documents[0].uuid
    )
    assert restored_semantic_document.deleted_at is None

    # Check we can fetch it
    response, parsed = swissfex_api_get_semantic_documents(
        authenticated_client, external_dossier_id, show_pages=True
    )
    assert response.status_code == 200
    assert len(parsed) == 1
    assert len(parsed[0].semantic_pages) == 5
    assert parsed[0].uuid == semantic_documents[0].uuid


def test_update_semantic_document(
    authenticated_client,
    swissfex_account,
    random_other_account,
    document_categories,
    set_swissfex_JWK,
):
    # Test updating a semantic document
    external_dossier_id = str(uuid.uuid4())

    dossier = Dossier.objects.create(
        external_id=external_dossier_id, account=swissfex_account
    )

    document_category = DocumentCategory.objects.filter(account=dossier.account).first()

    semantic_documents = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=[document_category.name],
        max_pages=5,
    )

    # Get Semantic documents
    response, parsed = swissfex_api_get_semantic_documents(
        authenticated_client, external_dossier_id, show_pages=True
    )
    assert response.status_code == 200

    assert len(parsed) == 1
    apidoc: SemanticDocument = parsed[0]
    assert apidoc.uuid == semantic_documents[0].uuid
    assert apidoc.external_semantic_document_id is None
    # assert apidoc.title_suffix is None    Cannot be tested as it is randomly assigned
    assert apidoc.custom_attribute is None

    semantic_document_uuid = semantic_documents[0].uuid

    for account in [dossier.account, random_other_account]:
        DocumentCategory.objects.create(
            account=account,
            name="NewKey",
            de="NewTitleDe",
            en="NewTitleEn",
            fr="NewTitleFr",
            it="NewTitleIt",
        )

    custom_attribute = "this is a custom attribute for the test"

    # Test failure case: update a document category that does not exist
    result = authenticated_client.patch(
        path=reverse(
            "swissfex-api:semantic-document-update",
            kwargs={
                "external_dossier_id": external_dossier_id,
                "semantic_document_uuid": semantic_document_uuid,
            },
        ),
        data=schemas.UpdateSemanticDocument(
            document_category_key="NewKeyThatDoesNotExist",
            custom_attribute=custom_attribute,
        ).model_dump_json(),
        content_type="application/json",
    )
    assert result.status_code == 404

    # Update semantic document
    result = authenticated_client.patch(
        path=reverse(
            "swissfex-api:semantic-document-update",
            kwargs={
                "external_dossier_id": external_dossier_id,
                "semantic_document_uuid": semantic_document_uuid,
            },
        ),
        data=schemas.UpdateSemanticDocument(
            document_category_key="NewKey",
            title_suffix="NewTitleSuffix",
            access_mode=schemas.AccessMode.READ_ONLY,
            external_semantic_document_id="ex777",
            custom_attribute=custom_attribute,
        ).model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 200

    parsed = schemas.SemanticDocument.model_validate_json(result.content)

    assert parsed.document_category_key == "NewKey"
    assert parsed.title_suffix == "NewTitleSuffix"
    assert parsed.access_mode == schemas.AccessMode.READ_ONLY
    assert parsed.external_semantic_document_id == "ex777"
    assert parsed.custom_attribute == custom_attribute
    document = SemanticDocument.objects.get(uuid=semantic_document_uuid)

    assert document.document_category.name == "NewKey"
    assert document.title_suffix == "NewTitleSuffix"

    # Check that we can update individual fields rather than all fields
    DocumentCategory.objects.create(
        account=dossier.account,
        name="NewKey2",
        de="NewTitleDe2",
        en="NewTitleEn2",
        fr="NewTitleFr2",
        it="NewTitleIt2",
    )
    result = authenticated_client.patch(
        path=reverse(
            "swissfex-api:semantic-document-update",
            kwargs={
                "external_dossier_id": external_dossier_id,
                "semantic_document_uuid": semantic_document_uuid,
            },
        ),
        data=schemas.UpdateSemanticDocument(
            document_category_key="NewKey2"
        ).model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 200

    document = SemanticDocument.objects.get(uuid=semantic_document_uuid)

    assert document.document_category.name == "NewKey2"
    # Doesn't change old one
    assert document.title_suffix == "NewTitleSuffix"

    response, parsed = swissfex_api_get_semantic_documents(
        authenticated_client, external_dossier_id, show_pages=True
    )
    assert response.status_code == 200
    assert len(parsed) == 1
    apidoc: SemanticDocument = parsed[0]
    assert apidoc.document_category_key == "NewKey2"
    assert apidoc.title_suffix == "NewTitleSuffix"
    assert apidoc.custom_attribute == custom_attribute

    # Update some properties again to see if they can be updated
    custom_attribute2 = "another custom attribute"
    result = authenticated_client.patch(
        path=reverse(
            "swissfex-api:semantic-document-update",
            kwargs={
                "external_dossier_id": external_dossier_id,
                "semantic_document_uuid": semantic_document_uuid,
            },
        ),
        data=schemas.UpdateSemanticDocument(
            title_suffix="NewTitleSuffix2", custom_attribute=custom_attribute2
        ).model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 200

    document = SemanticDocument.objects.get(uuid=semantic_document_uuid)
    # Doesn't change old one
    assert document.document_category.name == "NewKey2"
    assert document.title_suffix == "NewTitleSuffix2"
    assert document.custom_attribute == custom_attribute2

    response, parsed = swissfex_api_get_semantic_documents(
        authenticated_client, external_dossier_id, show_pages=True
    )
    assert response.status_code == 200
    assert len(parsed) == 1
    apidoc: SemanticDocument = parsed[0]
    assert apidoc.document_category_key == "NewKey2"
    assert apidoc.title_suffix == "NewTitleSuffix2"
    assert apidoc.custom_attribute == custom_attribute2

    # Now delete the custom attribute
    result = authenticated_client.patch(
        path=reverse(
            "swissfex-api:semantic-document-update",
            kwargs={
                "external_dossier_id": external_dossier_id,
                "semantic_document_uuid": semantic_document_uuid,
            },
        ),
        data=schemas.UpdateSemanticDocument(custom_attribute="").model_dump_json(),
        content_type="application/json",
    )
    assert result.status_code == 200
    response, parsed = swissfex_api_get_semantic_documents(
        authenticated_client, external_dossier_id, show_pages=True
    )
    assert response.status_code == 200
    assert len(parsed) == 1
    apidoc: SemanticDocument = parsed[0]
    assert apidoc.custom_attribute is None

    # Test you can't change semantic document external id to one that already exists

    semantic_document_2 = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=[document_category.name],
        max_pages=5,
    )[0]
    semantic_document_2.external_semantic_document_id = "semantic_document_2_ext"
    semantic_document_2.save()
    result = authenticated_client.patch(
        path=reverse(
            "swissfex-api:semantic-document-update",
            kwargs={
                "external_dossier_id": external_dossier_id,
                "semantic_document_uuid": semantic_document_uuid,
            },
        ),
        data=schemas.UpdateSemanticDocument(
            external_semantic_document_id="semantic_document_2_ext"
        ).model_dump_json(),
        content_type="application/json",
    )
    assert result.status_code == 409


@pytest.mark.parametrize(
    ("completed"),
    [False, True],
)
def test_export_dossier_semantic_document_pdf_success(
    completed,
    mocked_get_dossier,
    authenticated_client,
    swissfex_account,
    document_categories,
    set_swissfex_JWK,
    mocker: MockerFixture,
):
    # Test kicking off the semantic document pdf export.
    # IF completed is set to true, then we also complete the export
    result, external_dossier_id = swissfex_api_post_create_dossier(authenticated_client)
    assert result.status_code == 201

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    semantic_documents = add_some_fake_semantic_documents(dossier=dossier)

    # Pick a first one
    semantic_document = semantic_documents[0]

    def mock_publish_side_effect(*args, **kwargs):
        request = SemanticDocumentPDFRequestV1.model_validate_json(kwargs["message"])
        response_json = process_semantic_dossier_pdf_request(
            semantic_document_pdf_request=request
        )

        if completed:
            set_semantic_document_export_done(response_json)

    mock_dispatch_publish_request = mocker.patch(
        "swissfex.api.publish",
        side_effect=mock_publish_side_effect,
    )

    # Request to generate an export
    response = authenticated_client.post(
        path=reverse(
            "swissfex-api:export-dossier-semantic-document-pdf",
            kwargs={
                "semantic_document_uuid": str(semantic_document.uuid),
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert response.status_code == 200
    mock_dispatch_publish_request.assert_called_once()

    assert response.status_code == 200

    semantic_document_export = SemanticDocumentExport.objects.first()

    assert (
        schemas.SemanticDocumentPDFExportRequest.model_validate_json(
            response.content
        ).uuid
        == semantic_document_export.uuid
    )

    # Check we can poll for dossier status
    response = authenticated_client.get(
        path=reverse(
            "swissfex-api:dossier-semantic-document-export-status",
            kwargs={
                "semantic_document_export_request_uuid": str(
                    semantic_document_export.uuid
                )
            },
        ),
    )

    assert response.status_code == 200

    parse = schemas.ExportStatus.model_validate_json(response.content)
    assert parse.export_uuid == semantic_document_export.uuid
    if completed:
        assert parse.status == "PROCESSED"
        assert parse.updated_at
        assert parse.dossier_url
    else:
        assert parse.status == "PROCESSING"
        assert parse.updated_at is None
        assert parse.dossier_url is None


def test_export_dossier_semantic_document_pdf_with_annotations(
    mocked_get_dossier,
    authenticated_client,
    swissfex_account,
    document_categories,
    set_swissfex_JWK,
    mocker: MockerFixture,
):
    # Create a dossier
    result, external_dossier_id = swissfex_api_post_create_dossier(authenticated_client)
    assert result.status_code == 201

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    semantic_documents = add_some_fake_semantic_documents(dossier=dossier)

    # Pick a first one
    semantic_document = semantic_documents[0]
    semantic_page = semantic_document.semantic_pages.first()

    # Add an annotation to the first page
    annotation_group_uuid = uuid.uuid4()
    SemanticPageUserAnnotations.objects.create(
        semantic_page=semantic_page,
        annotation_group_uuid=annotation_group_uuid,
        annotation_type="highlight",
        text="Test annotation",
        bbox_top=0.2,  # 20% from top
        bbox_left=0.3,  # 30% from left
        bbox_width=0.1,  # 10% of page width
        bbox_height=0.05,  # 5% of page height
        hexcolor="#FFFF00",
    )

    mock_dispatch_publish_request = mocker.patch(
        "swissfex.api.publish",
        side_effect=mock_publish_side_effect_services_rabbit_mq_publish,
    )

    # Request to generate an export
    response = authenticated_client.post(
        path=reverse(
            "swissfex-api:export-dossier-semantic-document-pdf",
            kwargs={
                "semantic_document_uuid": str(semantic_document.uuid),
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert response.status_code == 200
    mock_dispatch_publish_request.assert_called_once()

    semantic_document_export = SemanticDocumentExport.objects.get(
        uuid=schemas.SemanticDocumentPDFExportRequest.model_validate_json(
            response.content
        ).uuid
    )

    assert semantic_document_export.file
    assert semantic_document_export.file.get_fast_url()

    # Verify that the annotation was included in the request
    request_data = mock_dispatch_publish_request.call_args[1]["message"]
    request = SemanticDocumentPDFRequestV1.model_validate_json(request_data)

    # Find the semantic page in the request data
    exported_page = next(
        (
            page
            for doc in request.semantic_dossier.semantic_documents
            for page in doc.semantic_pages
            if str(page.uuid) == str(semantic_page.uuid)
        ),
        None,
    )

    assert exported_page is not None
    assert len(exported_page.annotations) == 1
    exported_annotation = exported_page.annotations[0]
    assert exported_annotation.text == "Test annotation"
    assert exported_annotation.annotation_type == "highlight"
    assert exported_annotation.bbox_top == 0.2
    assert exported_annotation.bbox_left == 0.3
    assert exported_annotation.bbox_width == 0.1
    assert exported_annotation.bbox_height == 0.05
    assert exported_annotation.hexcolor == "#FFFF00"

    # Verify the actual PDF content has the annotation
    pdf_url = semantic_document_export.file.get_fast_url()
    response = requests.get(pdf_url)
    pdf_reader = pypdf.PdfReader(BytesIO(response.content))

    # Check all pages for the annotation since we can't guarantee page ordering
    annotation_found = False
    for pdf_page in pdf_reader.pages:
        if "/Annots" not in pdf_page:
            continue

        if len(pdf_page["/Annots"]) != 1:
            continue

        # Get the annotation and verify its properties
        annotation = pdf_page["/Annots"][0].get_object()
        if annotation["/Subtype"] == "/Highlight" and annotation["/C"] == [
            1,
            1,
            0,
        ]:  # Yellow in RGB
            annotation_found = True

            # Verify the annotation position (with some tolerance for rounding)
            page_height = float(pdf_page.mediabox.height)
            page_width = float(pdf_page.mediabox.width)

            expected_left = 0.3 * page_width
            expected_width = 0.1 * page_width
            expected_top = page_height - (0.2 * page_height)
            expected_height = 0.05 * page_height

            rect = annotation["/Rect"]
            rect_left = float(rect[0])
            rect_right = float(rect[2])
            rect_bottom = float(rect[1])
            rect_top = float(rect[3])

            # Allow for small rounding differences
            assert abs(rect_left - expected_left) < 1  # Left
            assert abs(rect_right - (expected_left + expected_width)) < 1  # Right
            assert abs(rect_bottom - (expected_top - expected_height)) < 1  # Bottom
            assert abs(rect_top - expected_top) < 1  # Top
            break

    # Verify that we found the annotation
    assert (
        annotation_found
    ), "Could not find the expected annotation in any page of the PDF"


@pytest.mark.parametrize(
    ("async_api", "response_schema"),
    [(False, schemas.DossierCopyResponse), (True, schemas.DossierCopyAsyncResponse)],
)
@pytest.mark.parametrize(
    ("name", "language", "access_mode", "include_deleted"),
    [
        (None, None, None, None),  # No optional params
        (None, None, None, True),  # Keep deleted, same as above
        (None, None, None, False),  # Delete soft deleted semantic documents and pages
        ("New Dossier", "fr", "read_write", False),  # All optional params
        ("Only Name", None, None, None),  # Only name
        (None, "it", None, None),  # Only language
        (None, None, "read_only", None),  # Only access_mode
    ],
)
def test_copy_dossier_api(
    authenticated_client,
    swissfex_account,
    document_categories,
    set_swissfex_JWK,
    async_api,
    response_schema,
    name,
    language,
    access_mode,
    include_deleted,
    mocker: MockerFixture,
):
    external_dossier_id = str(uuid.uuid4())

    # Create a dossier
    create_dossier_response = authenticated_client.post(
        path=reverse("swissfex-api:create-dossier"),
        data=schemas.CreateDossier(
            name="Test Dossier",
            external_dossier_id=external_dossier_id,
            language="de",
        ).model_dump_json(),
        content_type="application/json",
    ).status_code

    # For debugging as we get intermittent failures here
    if create_dossier_response != 201:
        logger.info(f"Failed to create dossier: {create_dossier_response.content}")

    assert create_dossier_response == 201

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    add_some_fake_semantic_documents(
        dossier=dossier, num_docs=10, max_pages=5, min_num_pages=5
    )

    # We want to test include_deleted feature, so:
    # Create a semantic document and soft delete it i.e. expire it
    sem_doc_soft_deleted = add_some_fake_semantic_documents(
        dossier=dossier, num_docs=1, max_pages=5, min_num_pages=5
    )[0]

    sem_doc_soft_deleted.deleted_at = timezone.now() - timedelta(days=1)
    sem_doc_soft_deleted.title_custom = "Soft Deleted"
    sem_doc_soft_deleted.save()

    sem_doc_sem_page_soft_deleted = add_some_fake_semantic_documents(
        dossier=dossier, num_docs=1, max_pages=5, min_num_pages=5
    )[0]

    sem_doc_sem_page_soft_deleted.title_custom = "Page Soft Deleted"
    sem_doc_sem_page_soft_deleted.save()

    sem_doc_sem_page_soft_deleted.semantic_pages.all().update(
        deleted_at=timezone.now() - timedelta(days=1)
    )

    target_dossier_external_id = "targetdossierid123"

    if async_api:
        view_name = "swissfex-api:copy-dossier-async"

        # The async version creates a new dossier and copies relevant models in an async job
        # this is at the request of SwissFex so that an (empty) dossier already exists
        def mock_publish_request_side_effect(*args, **kwargs):
            copy_dossier_into_existing_dossier(kwargs["message"])

    else:

        view_name = "swissfex-api:copy-dossier"

        def mock_publish_request_side_effect(*args, **kwargs):
            copy_dossier_consumer(kwargs["message"])

    mocker.patch(
        "swissfex.api.publish",
        side_effect=mock_publish_request_side_effect,
    )

    copy_dossier_data = {
        "new_external_dossier_id": target_dossier_external_id,
    }
    if name:
        copy_dossier_data["name"] = name
    if language:
        copy_dossier_data["language"] = language
    if access_mode:
        copy_dossier_data["access_mode"] = access_mode

    response = authenticated_client.post(
        path=reverse(
            view_name,
            kwargs={"external_dossier_id": external_dossier_id},
        ),
        data=schemas.CopyDossier(**copy_dossier_data).model_dump_json(),
        content_type="application/json",
    )

    assert response.status_code == 201

    parse = response_schema.model_validate_json(response.content)

    assert parse.external_dossier_id == target_dossier_external_id

    new_dossier = Dossier.objects.get(
        external_id=target_dossier_external_id, account=dossier.account
    )

    # Check optional parameters
    if name:
        assert new_dossier.name == name
    else:
        assert new_dossier.name == dossier.name

    if language:
        assert new_dossier.lang == language
    else:
        assert new_dossier.lang == dossier.lang

    if access_mode:
        # All semantic documents should have the same access mode
        assert (
            SemanticDocument._base_manager.filter(
                dossier=new_dossier, access_mode=access_mode
            ).count()
            == SemanticDocument._base_manager.filter(dossier=new_dossier).count()
        )

    if include_deleted:
        assert (
            SemanticDocument._base_manager.filter(
                dossier=new_dossier,
                deleted_at__isnull=False,
                title_custom="Soft Deleted",
            ).exists()
            == include_deleted
        )

        assert (
            SemanticPage._base_manager.filter(
                semantic_document__dossier=new_dossier,
                semantic_document__title_custom="Page Soft Deleted",
            ).exists()
            == include_deleted
        )

    semantic_document_count = SemanticDocument.objects.filter(
        dossier=new_dossier
    ).count()

    assert semantic_document_count > 0

    assert (
        semantic_document_count
        == SemanticDocument.objects.filter(dossier=dossier).count()
    )

    # Delete the Original Dossier and check we can still fetch the new one
    dossier.delete()

    new_dossier = Dossier.objects.get(external_id=target_dossier_external_id)

    assert (
        semantic_document_count
        == SemanticDocument.objects.filter(dossier=new_dossier).count()
    )

    # Rely on tests on copy_dossier_models to check for proper behaviour of model cloning


def test_dossier_copy_status(
    authenticated_client,
    swissfex_account,
    document_categories,
    set_swissfex_JWK,
):
    external_dossier_id = str(uuid.uuid4())

    dossier_copy_status = DossierCopyStatus.objects.create(
        target_external_id=external_dossier_id,
        account=swissfex_account,
        source_dossier_uuid=uuid.uuid4(),
    )

    # Check we can poll for dossier status
    response = authenticated_client.get(
        path=reverse(
            "swissfex-api:copy-dossier-status",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
    )

    assert response.status_code == 200

    parse = schemas.DossierCopyStatus.model_validate_json(response.content)
    assert parse.external_dossier_id == external_dossier_id
    assert parse.status == "PROCESSING"

    dossier_copy_status.done = timezone.now()
    dossier_copy_status.save()

    # Check we can poll for dossier status
    response = authenticated_client.get(
        path=reverse(
            "swissfex-api:copy-dossier-status",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
    )

    assert response.status_code == 200

    parse = schemas.DossierCopyStatus.model_validate_json(response.content)
    assert parse.external_dossier_id == external_dossier_id
    assert parse.status == "PROCESSED"
