context('dossier editor', () => {
    before(function () {
        cy.keycloakLogin();
    });

    it('Find the element and click it. The dossier-editor should open up.', function () {
        cy.contains("Neues Dossier erstellen")

        cy.get('tbody', {timeout: 10000}) // Awaiting the list of dossiers to be loaded
            .find('tr').first().click()
        cy.url().should('include', '/editor')
        cy.url().should('include', '/view/')

        cy.screenshot()
    });
})
